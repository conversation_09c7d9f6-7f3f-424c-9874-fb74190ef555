const { app, BrowserWindow, Menu, ipcMain } = require('electron');
const path = require('path');
const Database = require('./src/database/database');

let mainWindow;
let database;

function createWindow() {
    // إنشاء النافذة الرئيسية
    mainWindow = new BrowserWindow({
        width: 1400,
        height: 900,
        minWidth: 1000,
        minHeight: 600,
        webPreferences: {
            nodeIntegration: true,
            contextIsolation: false,
            enableRemoteModule: true
        },
        icon: path.join(__dirname, 'assets/icon.png'),
        show: false,
        titleBarStyle: 'default'
    });

    // تحميل الصفحة الرئيسية
    mainWindow.loadFile('src/views/login.html');

    // إظهار النافذة عند الانتهاء من التحميل
    mainWindow.once('ready-to-show', () => {
        mainWindow.show();
        mainWindow.maximize();
    });

    // إعداد القائمة
    const template = [
        {
            label: 'ملف',
            submenu: [
                {
                    label: 'خروج',
                    accelerator: 'CmdOrCtrl+Q',
                    click: () => {
                        app.quit();
                    }
                }
            ]
        },
        {
            label: 'عرض',
            submenu: [
                {
                    label: 'إعادة تحميل',
                    accelerator: 'CmdOrCtrl+R',
                    click: () => {
                        mainWindow.reload();
                    }
                },
                {
                    label: 'أدوات المطور',
                    accelerator: 'F12',
                    click: () => {
                        mainWindow.webContents.toggleDevTools();
                    }
                }
            ]
        },
        {
            label: 'مساعدة',
            submenu: [
                {
                    label: 'حول البرنامج',
                    click: () => {
                        // يمكن إضافة نافذة معلومات البرنامج هنا
                    }
                }
            ]
        }
    ];

    const menu = Menu.buildFromTemplate(template);
    Menu.setApplicationMenu(menu);

    mainWindow.on('closed', () => {
        mainWindow = null;
    });
}

// تهيئة التطبيق
app.whenReady().then(async () => {
    // تهيئة قاعدة البيانات
    database = new Database();
    await database.initialize();
    
    createWindow();

    app.on('activate', () => {
        if (BrowserWindow.getAllWindows().length === 0) {
            createWindow();
        }
    });
});

app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

// معالجات IPC للتواصل مع واجهة المستخدم
ipcMain.handle('database-query', async (event, query, params) => {
    try {
        return await database.query(query, params);
    } catch (error) {
        console.error('Database query error:', error);
        throw error;
    }
});

ipcMain.handle('database-run', async (event, query, params) => {
    try {
        return await database.run(query, params);
    } catch (error) {
        console.error('Database run error:', error);
        throw error;
    }
});

ipcMain.handle('database-get', async (event, query, params) => {
    try {
        return await database.get(query, params);
    } catch (error) {
        console.error('Database get error:', error);
        throw error;
    }
});

ipcMain.handle('database-all', async (event, query, params) => {
    try {
        return await database.all(query, params);
    } catch (error) {
        console.error('Database all error:', error);
        throw error;
    }
});

// تصدير المتغيرات للاستخدام في ملفات أخرى
module.exports = { mainWindow, database };
