// ملف إصلاحات سريعة للتأكد من عمل جميع الوظائف

// التأكد من تحميل جميع الدوال عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // إصلاح مشكلة عدم ظهور النوافذ المنبثقة
    fixModalIssues();
    
    // إصلاح مشكلة الأزرار
    fixButtonIssues();
    
    // إصلاح مشكلة القوائم المنسدلة
    fixDropdownIssues();
});

// إصلاح مشاكل النوافذ المنبثقة
function fixModalIssues() {
    // التأكد من وجود جميع النوافذ المنبثقة
    const modals = [
        'customerModal',
        'customerDetailsModal', 
        'deviceModal',
        'sparePartModal',
        'userModal',
        'userSessionsModal'
    ];
    
    modals.forEach(modalId => {
        const modal = document.getElementById(modalId);
        if (modal) {
            // إضافة معالج النقر على الخلفية
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    modal.classList.remove('show');
                }
            });
            
            // إضافة معالج لأزرار الإغلاق
            const closeButtons = modal.querySelectorAll('.modal-close');
            closeButtons.forEach(button => {
                button.addEventListener('click', function() {
                    modal.classList.remove('show');
                });
            });
        }
    });
}

// إصلاح مشاكل الأزرار
function fixButtonIssues() {
    // التأكد من عمل أزرار الإضافة
    const addButtons = [
        { selector: '[onclick="showAddCustomerModal()"]', action: showAddCustomerModal },
        { selector: '[onclick="showAddDeviceModal()"]', action: showAddDeviceModal },
        { selector: '[onclick="showAddSparePartModal()"]', action: showAddSparePartModal },
        { selector: '[onclick="showAddUserModal()"]', action: showAddUserModal }
    ];
    
    addButtons.forEach(({ selector, action }) => {
        const buttons = document.querySelectorAll(selector);
        buttons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                if (typeof action === 'function') {
                    action();
                }
            });
        });
    });
    
    // إصلاح زر البيانات التجريبية
    const sampleDataButton = document.querySelector('[onclick="addSampleData()"]');
    if (sampleDataButton) {
        sampleDataButton.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            if (typeof window.addSampleData === 'function') {
                window.addSampleData();
            }
        });
    }
}

// إصلاح مشاكل القوائم المنسدلة
function fixDropdownIssues() {
    // إصلاح قائمة المستخدم
    const userButton = document.querySelector('.user-btn');
    const userDropdown = document.getElementById('userDropdown');
    
    if (userButton && userDropdown) {
        userButton.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            userDropdown.classList.toggle('show');
        });
    }
    
    // إصلاح زر الإشعارات
    const notificationButton = document.querySelector('.notification-btn');
    const notificationPanel = document.getElementById('notificationPanel');
    
    if (notificationButton && notificationPanel) {
        notificationButton.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            notificationPanel.classList.toggle('show');
        });
    }
}

// دوال مساعدة للتأكد من عمل النوافذ المنبثقة
function ensureModalWorks(modalId, formId, titleId, titleText) {
    const modal = document.getElementById(modalId);
    const form = document.getElementById(formId);
    const title = document.getElementById(titleId);
    
    if (modal && form && title) {
        title.textContent = titleText;
        form.reset();
        modal.classList.add('show');
        return true;
    }
    return false;
}

// إعادة تعريف الدوال الرئيسية للتأكد من عملها
window.showAddCustomerModal = function() {
    showSection('customers');
    setTimeout(() => {
        if (typeof initializeCustomers === 'function') {
            initializeCustomers();
        }
        setTimeout(() => {
            ensureModalWorks('customerModal', 'customerForm', 'modalTitle', 'إضافة عميل جديد');
        }, 200);
    }, 100);
};

window.showAddDeviceModal = function() {
    showSection('devices');
    setTimeout(() => {
        if (typeof initializeDevices === 'function') {
            initializeDevices();
        }
        setTimeout(() => {
            if (ensureModalWorks('deviceModal', 'deviceForm', 'deviceModalTitle', 'إضافة جهاز جديد')) {
                // تعيين التاريخ الحالي
                const today = new Date().toISOString().split('T')[0];
                const receivedDateInput = document.getElementById('receivedDate');
                if (receivedDateInput) {
                    receivedDateInput.value = today;
                }
            }
        }, 200);
    }, 100);
};

window.showAddSparePartModal = function() {
    showSection('spare-parts');
    setTimeout(() => {
        if (typeof initializeSpareParts === 'function') {
            initializeSpareParts();
        }
        setTimeout(() => {
            ensureModalWorks('sparePartModal', 'sparePartForm', 'sparePartModalTitle', 'إضافة قطعة غيار جديدة');
        }, 200);
    }, 100);
};

window.showAddUserModal = function() {
    showSection('users');
    setTimeout(() => {
        if (typeof initializeUsers === 'function') {
            initializeUsers();
        }
        setTimeout(() => {
            if (ensureModalWorks('userModal', 'userForm', 'userModalTitle', 'إضافة مستخدم جديد')) {
                // إظهار حقل كلمة المرور
                const passwordGroup = document.getElementById('passwordGroup');
                if (passwordGroup) {
                    passwordGroup.style.display = 'block';
                    const passwordInput = document.getElementById('userPassword');
                    if (passwordInput) {
                        passwordInput.required = true;
                    }
                }
            }
        }, 200);
    }, 100);
};

// دوال إخفاء النوافذ المنبثقة
window.hideCustomerModal = () => hideModal('customerModal');
window.hideCustomerDetailsModal = () => hideModal('customerDetailsModal');
window.hideDeviceModal = () => hideModal('deviceModal');
window.hideSparePartModal = () => hideModal('sparePartModal');
window.hideUserModal = () => hideModal('userModal');
window.hideUserSessionsModal = () => hideModal('userSessionsModal');

function hideModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.remove('show');
    }
}

// دالة إضافة البيانات التجريبية
window.addSampleData = async function() {
    if (typeof addSampleData === 'function') {
        try {
            await addSampleData();
            alert('تم إضافة البيانات التجريبية بنجاح! يرجى إعادة تحميل الصفحة لرؤية البيانات.');
            location.reload();
        } catch (error) {
            console.error('خطأ في إضافة البيانات التجريبية:', error);
            alert('حدث خطأ في إضافة البيانات التجريبية');
        }
    } else {
        alert('دالة إضافة البيانات التجريبية غير متاحة');
    }
};

// إصلاح مشكلة تبديل القائمة الجانبية
window.toggleSidebar = function() {
    const sidebar = document.getElementById('sidebar');
    if (sidebar) {
        sidebar.classList.toggle('collapsed');
    }
};

// إصلاح مشكلة تبديل قائمة المستخدم
window.toggleUserMenu = function() {
    const dropdown = document.getElementById('userDropdown');
    if (dropdown) {
        dropdown.classList.toggle('show');
    }
};

// إصلاح مشكلة الإشعارات
window.showNotifications = function() {
    const panel = document.getElementById('notificationPanel');
    if (panel) {
        panel.classList.add('show');
    }
};

window.hideNotifications = function() {
    const panel = document.getElementById('notificationPanel');
    if (panel) {
        panel.classList.remove('show');
    }
};

// إصلاح مشكلة تسجيل الخروج
window.logout = function() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        sessionStorage.clear();
        localStorage.clear();
        window.location.href = 'login.html';
    }
};

console.log('تم تحميل إصلاحات النظام بنجاح');
