const { ipcRenderer } = require('electron');

// متغيرات عامة
let devices = [];
let customers = [];
let engineers = [];
let currentDevice = null;
let isEditMode = false;

// تهيئة صفحة الأجهزة
function initializeDevices() {
    loadDevices();
    loadCustomers();
    loadEngineers();
    setupDeviceEventListeners();
}

// إعداد مستمعي الأحداث
function setupDeviceEventListeners() {
    // نموذج إضافة/تعديل الجهاز
    const deviceForm = document.getElementById('deviceForm');
    if (deviceForm) {
        deviceForm.addEventListener('submit', handleDeviceSubmit);
    }
    
    // البحث
    const searchInput = document.getElementById('deviceSearch');
    if (searchInput) {
        searchInput.addEventListener('input', handleDeviceSearch);
    }
    
    // فلترة حسب الحالة
    const statusFilter = document.getElementById('statusFilter');
    if (statusFilter) {
        statusFilter.addEventListener('change', handleStatusFilter);
    }
    
    // اختيار العميل
    const customerSelect = document.getElementById('deviceCustomer');
    if (customerSelect) {
        customerSelect.addEventListener('change', handleCustomerSelect);
    }
}

// تحميل الأجهزة
async function loadDevices() {
    try {
        devices = await ipcRenderer.invoke('database-all', `
            SELECT d.*, c.name as customer_name, c.phone as customer_phone,
                   u.full_name as engineer_name
            FROM devices d 
            LEFT JOIN customers c ON d.customer_id = c.id 
            LEFT JOIN users u ON d.assigned_engineer_id = u.id
            ORDER BY d.created_at DESC
        `);
        
        displayDevices(devices);
        updateDevicesStats();
        
    } catch (error) {
        console.error('خطأ في تحميل الأجهزة:', error);
        showNotification('حدث خطأ في تحميل الأجهزة', 'error');
    }
}

// تحميل العملاء
async function loadCustomers() {
    try {
        customers = await ipcRenderer.invoke('database-all', 
            'SELECT * FROM customers ORDER BY name ASC'
        );
        
        populateCustomerSelect();
        
    } catch (error) {
        console.error('خطأ في تحميل العملاء:', error);
    }
}

// تحميل المهندسين
async function loadEngineers() {
    try {
        engineers = await ipcRenderer.invoke('database-all', 
            'SELECT * FROM users WHERE role = "engineer" AND is_active = 1 ORDER BY full_name ASC'
        );
        
        populateEngineerSelect();
        
    } catch (error) {
        console.error('خطأ في تحميل المهندسين:', error);
    }
}

// ملء قائمة العملاء
function populateCustomerSelect() {
    const select = document.getElementById('deviceCustomer');
    if (!select) return;
    
    select.innerHTML = '<option value="">اختر العميل...</option>';
    
    customers.forEach(customer => {
        const option = document.createElement('option');
        option.value = customer.id;
        option.textContent = `${customer.name} - ${customer.phone}`;
        select.appendChild(option);
    });
}

// ملء قائمة المهندسين
function populateEngineerSelect() {
    const select = document.getElementById('assignedEngineer');
    if (!select) return;
    
    select.innerHTML = '<option value="">اختر المهندس...</option>';
    
    engineers.forEach(engineer => {
        const option = document.createElement('option');
        option.value = engineer.id;
        option.textContent = engineer.full_name;
        select.appendChild(option);
    });
}

// عرض الأجهزة
function displayDevices(devicesToShow) {
    const container = document.getElementById('devicesContainer');
    if (!container) return;
    
    container.innerHTML = '';
    
    if (devicesToShow.length === 0) {
        container.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-mobile-alt"></i>
                <h3>لا توجد أجهزة</h3>
                <p>ابدأ بإضافة جهاز جديد</p>
                <button class="btn btn-primary" onclick="showAddDeviceModal()">
                    <i class="fas fa-plus"></i>
                    إضافة جهاز جديد
                </button>
            </div>
        `;
        return;
    }
    
    devicesToShow.forEach(device => {
        const deviceCard = createDeviceCard(device);
        container.appendChild(deviceCard);
    });
}

// إنشاء بطاقة جهاز
function createDeviceCard(device) {
    const div = document.createElement('div');
    div.className = 'device-card';
    
    const statusClass = getStatusClass(device.status);
    const statusName = getStatusDisplayName(device.status);
    
    div.innerHTML = `
        <div class="device-header">
            <div class="device-icon">
                <i class="fas ${getDeviceIcon(device.device_type)}"></i>
            </div>
            <div class="device-info">
                <h3>${device.brand} ${device.model}</h3>
                <p><i class="fas fa-user"></i> ${device.customer_name || 'غير محدد'}</p>
                <p><i class="fas fa-phone"></i> ${device.customer_phone || 'غير محدد'}</p>
            </div>
            <div class="device-status">
                <span class="status-badge ${statusClass}">${statusName}</span>
            </div>
        </div>
        
        <div class="device-details">
            <div class="detail-row">
                <span class="detail-label">نوع الجهاز:</span>
                <span class="detail-value">${device.device_type}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">المشكلة:</span>
                <span class="detail-value">${device.problem_description}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">المهندس المسؤول:</span>
                <span class="detail-value">${device.engineer_name || 'غير محدد'}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">تاريخ الاستلام:</span>
                <span class="detail-value">${formatDate(device.received_date)}</span>
            </div>
            ${device.expected_delivery_date ? `
                <div class="detail-row">
                    <span class="detail-label">التسليم المتوقع:</span>
                    <span class="detail-value">${formatDate(device.expected_delivery_date)}</span>
                </div>
            ` : ''}
        </div>
        
        <div class="device-footer">
            <div class="device-cost">
                <span class="cost-label">التكلفة:</span>
                <span class="cost-value">${formatCurrency(device.final_cost || device.estimated_cost || 0)}</span>
            </div>
            <div class="device-actions">
                <button class="btn-icon" onclick="viewDevice(${device.id})" title="عرض التفاصيل">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="btn-icon" onclick="editDevice(${device.id})" title="تعديل">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn-icon" onclick="updateDeviceStatus(${device.id})" title="تحديث الحالة">
                    <i class="fas fa-sync"></i>
                </button>
                <button class="btn-icon" onclick="printReceipt(${device.id})" title="طباعة الإيصال">
                    <i class="fas fa-print"></i>
                </button>
            </div>
        </div>
    `;
    
    return div;
}

// الحصول على أيقونة الجهاز
function getDeviceIcon(deviceType) {
    const icons = {
        'هاتف': 'fa-mobile-alt',
        'لابتوب': 'fa-laptop',
        'تابلت': 'fa-tablet-alt',
        'شاشة': 'fa-tv',
        'ساعة ذكية': 'fa-clock',
        'سماعة': 'fa-headphones',
        'أخرى': 'fa-cog'
    };
    return icons[deviceType] || 'fa-cog';
}

// الحصول على فئة الحالة
function getStatusClass(status) {
    const classes = {
        'received': 'status-received',
        'inspecting': 'status-inspecting',
        'repairing': 'status-repairing',
        'completed': 'status-completed',
        'delivered': 'status-delivered',
        'cancelled': 'status-cancelled'
    };
    return classes[status] || 'status-received';
}

// الحصول على اسم الحالة للعرض
function getStatusDisplayName(status) {
    const statuses = {
        'received': 'مستلم',
        'inspecting': 'قيد الفحص',
        'repairing': 'قيد الإصلاح',
        'completed': 'مكتمل',
        'delivered': 'مُسلم',
        'cancelled': 'ملغي'
    };
    return statuses[status] || status;
}

// إظهار نافذة إضافة جهاز
function showAddDeviceModal() {
    isEditMode = false;
    currentDevice = null;

    const modal = document.getElementById('deviceModal');
    const form = document.getElementById('deviceForm');
    const title = document.getElementById('deviceModalTitle');

    if (modal && form && title) {
        title.textContent = 'إضافة جهاز جديد';
        form.reset();

        // تعيين التاريخ الحالي
        const today = new Date().toISOString().split('T')[0];
        const receivedDateInput = document.getElementById('receivedDate');
        if (receivedDateInput) {
            receivedDateInput.value = today;
        }

        modal.classList.add('show');
    }
}

// جعل الدالة متاحة عالمياً
window.showAddDeviceModal = showAddDeviceModal;

// معالج اختيار العميل
function handleCustomerSelect(e) {
    const customerId = e.target.value;
    if (!customerId) return;
    
    const customer = customers.find(c => c.id == customerId);
    if (customer) {
        // ملء بيانات العميل تلقائياً
        const customerInfo = document.getElementById('customerInfo');
        if (customerInfo) {
            customerInfo.innerHTML = `
                <p><strong>الهاتف:</strong> ${customer.phone}</p>
                ${customer.email ? `<p><strong>البريد:</strong> ${customer.email}</p>` : ''}
                ${customer.address ? `<p><strong>العنوان:</strong> ${customer.address}</p>` : ''}
            `;
        }
    }
}

// معالج إرسال نموذج الجهاز
async function handleDeviceSubmit(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const deviceData = {
        customer_id: formData.get('customer_id'),
        device_type: formData.get('device_type'),
        brand: formData.get('brand'),
        model: formData.get('model'),
        color: formData.get('color'),
        serial_number: formData.get('serial_number'),
        password: formData.get('password'),
        problem_description: formData.get('problem_description'),
        accessories: formData.get('accessories'),
        received_date: formData.get('received_date'),
        inspection_fee: parseFloat(formData.get('inspection_fee')) || 0,
        estimated_cost: parseFloat(formData.get('estimated_cost')) || 0,
        expected_delivery_date: formData.get('expected_delivery_date'),
        assigned_engineer_id: formData.get('assigned_engineer_id') || null,
        notes: formData.get('notes')
    };
    
    // التحقق من صحة البيانات
    if (!validateDeviceData(deviceData)) {
        return;
    }
    
    try {
        if (isEditMode && currentDevice) {
            // تحديث الجهاز
            await ipcRenderer.invoke('database-run', `
                UPDATE devices SET 
                    customer_id = ?, device_type = ?, brand = ?, model = ?, color = ?,
                    serial_number = ?, password = ?, problem_description = ?, accessories = ?,
                    received_date = ?, inspection_fee = ?, estimated_cost = ?, 
                    expected_delivery_date = ?, assigned_engineer_id = ?, notes = ?,
                    updated_at = ?
                WHERE id = ?
            `, [
                deviceData.customer_id, deviceData.device_type, deviceData.brand, 
                deviceData.model, deviceData.color, deviceData.serial_number,
                deviceData.password, deviceData.problem_description, deviceData.accessories,
                deviceData.received_date, deviceData.inspection_fee, deviceData.estimated_cost,
                deviceData.expected_delivery_date, deviceData.assigned_engineer_id, 
                deviceData.notes, new Date().toISOString(), currentDevice.id
            ]);
            
            showNotification('تم تحديث بيانات الجهاز بنجاح', 'success');
        } else {
            // إضافة جهاز جديد
            const result = await ipcRenderer.invoke('database-run', `
                INSERT INTO devices (
                    customer_id, device_type, brand, model, color, serial_number,
                    password, problem_description, accessories, received_date,
                    inspection_fee, estimated_cost, expected_delivery_date,
                    assigned_engineer_id, notes, status
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'received')
            `, [
                deviceData.customer_id, deviceData.device_type, deviceData.brand,
                deviceData.model, deviceData.color, deviceData.serial_number,
                deviceData.password, deviceData.problem_description, deviceData.accessories,
                deviceData.received_date, deviceData.inspection_fee, deviceData.estimated_cost,
                deviceData.expected_delivery_date, deviceData.assigned_engineer_id, deviceData.notes
            ]);
            
            // تسجيل حالة الجهاز
            await ipcRenderer.invoke('database-run', `
                INSERT INTO device_status_log (device_id, new_status, changed_by, notes)
                VALUES (?, 'received', ?, 'تم استلام الجهاز')
            `, [result.id, getCurrentUser().id]);
            
            showNotification('تم إضافة الجهاز بنجاح', 'success');
            
            // عرض خيار طباعة الإيصال
            if (confirm('هل تريد طباعة إيصال الاستلام؟')) {
                printReceipt(result.id);
            }
        }
        
        hideDeviceModal();
        loadDevices();
        
    } catch (error) {
        console.error('خطأ في حفظ بيانات الجهاز:', error);
        showNotification('حدث خطأ في حفظ بيانات الجهاز', 'error');
    }
}

// التحقق من صحة بيانات الجهاز
function validateDeviceData(data) {
    if (!data.customer_id) {
        showNotification('يرجى اختيار العميل', 'error');
        return false;
    }
    
    if (!data.device_type) {
        showNotification('يرجى إدخال نوع الجهاز', 'error');
        return false;
    }
    
    if (!data.brand) {
        showNotification('يرجى إدخال ماركة الجهاز', 'error');
        return false;
    }
    
    if (!data.model) {
        showNotification('يرجى إدخال موديل الجهاز', 'error');
        return false;
    }
    
    if (!data.problem_description) {
        showNotification('يرجى وصف المشكلة', 'error');
        return false;
    }
    
    return true;
}

// البحث في الأجهزة
function handleDeviceSearch(e) {
    const searchTerm = e.target.value.toLowerCase().trim();
    
    if (!searchTerm) {
        displayDevices(devices);
        return;
    }
    
    const filteredDevices = devices.filter(device => 
        device.brand.toLowerCase().includes(searchTerm) ||
        device.model.toLowerCase().includes(searchTerm) ||
        device.device_type.toLowerCase().includes(searchTerm) ||
        (device.customer_name && device.customer_name.toLowerCase().includes(searchTerm)) ||
        (device.serial_number && device.serial_number.toLowerCase().includes(searchTerm))
    );
    
    displayDevices(filteredDevices);
}

// فلترة حسب الحالة
function handleStatusFilter(e) {
    const status = e.target.value;
    
    if (!status) {
        displayDevices(devices);
        return;
    }
    
    const filteredDevices = devices.filter(device => device.status === status);
    displayDevices(filteredDevices);
}

// تحديث إحصائيات الأجهزة
function updateDevicesStats() {
    const totalDevices = devices.length;
    const pendingDevices = devices.filter(d => ['received', 'inspecting', 'repairing'].includes(d.status)).length;
    const completedDevices = devices.filter(d => d.status === 'completed').length;
    const deliveredDevices = devices.filter(d => d.status === 'delivered').length;
    
    // تحديث العناصر في الواجهة
    const totalElement = document.getElementById('totalDevicesCount');
    const pendingElement = document.getElementById('pendingDevicesCount');
    const completedElement = document.getElementById('completedDevicesCount');
    const deliveredElement = document.getElementById('deliveredDevicesCount');
    
    if (totalElement) totalElement.textContent = totalDevices;
    if (pendingElement) pendingElement.textContent = pendingDevices;
    if (completedElement) completedElement.textContent = completedDevices;
    if (deliveredElement) deliveredElement.textContent = deliveredDevices;
}

// إخفاء نافذة الجهاز
function hideDeviceModal() {
    const modal = document.getElementById('deviceModal');
    modal.classList.remove('show');
}

// دوال مؤقتة
function viewDevice(deviceId) {
    alert(`عرض تفاصيل الجهاز ${deviceId} قيد التطوير`);
}

function editDevice(deviceId) {
    alert(`تعديل الجهاز ${deviceId} قيد التطوير`);
}

function updateDeviceStatus(deviceId) {
    alert(`تحديث حالة الجهاز ${deviceId} قيد التطوير`);
}

function printReceipt(deviceId) {
    alert(`طباعة إيصال الجهاز ${deviceId} قيد التطوير`);
}

// دوال مساعدة
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA');
}

function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-SA', {
        style: 'decimal',
        minimumFractionDigits: 0,
        maximumFractionDigits: 2
    }).format(amount || 0) + ' ريال';
}

function showNotification(message, type = 'info') {
    if (type === 'error') {
        alert('خطأ: ' + message);
    } else if (type === 'success') {
        alert('نجح: ' + message);
    } else {
        alert(message);
    }
}

// تصدير الدوال
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        initializeDevices,
        showAddDeviceModal,
        hideDeviceModal
    };
}
