const { ipcRenderer } = require('electron');

// متغيرات عامة
let reportsData = {};

// تهيئة صفحة التقارير
function initializeReports() {
    loadReportsData();
    setupReportsEventListeners();
}

// إعداد مستمعي الأحداث
function setupReportsEventListeners() {
    // نوع التقرير
    const reportType = document.getElementById('reportType');
    if (reportType) {
        reportType.addEventListener('change', handleReportTypeChange);
    }
    
    // فترة الرسم البياني
    const revenueChartPeriod = document.getElementById('revenueChartPeriod');
    if (revenueChartPeriod) {
        revenueChartPeriod.addEventListener('change', updateRevenueChart);
    }
}

// تحميل بيانات التقارير
async function loadReportsData() {
    try {
        // الحصول على التاريخ الحالي
        const now = new Date();
        const currentMonth = now.toISOString().slice(0, 7);
        const currentYear = now.getFullYear();
        
        // إيرادات الشهر الحالي
        const monthlyRevenue = await ipcRenderer.invoke('database-get', `
            SELECT SUM(final_cost) as total 
            FROM devices 
            WHERE DATE(created_at) LIKE ? AND status = 'delivered'
        `, [`${currentMonth}%`]);
        
        // عدد الأجهزة هذا الشهر
        const devicesThisMonth = await ipcRenderer.invoke('database-get', `
            SELECT COUNT(*) as count 
            FROM devices 
            WHERE DATE(created_at) LIKE ?
        `, [`${currentMonth}%`]);
        
        // العملاء الجدد هذا الشهر
        const newCustomers = await ipcRenderer.invoke('database-get', `
            SELECT COUNT(*) as count 
            FROM customers 
            WHERE DATE(created_at) LIKE ?
        `, [`${currentMonth}%`]);
        
        // قيمة المخزون
        const stockValue = await ipcRenderer.invoke('database-get', `
            SELECT SUM(quantity_in_stock * cost_price) as total 
            FROM spare_parts
        `);
        
        // الإيرادات الشهرية للرسم البياني
        const monthlyRevenueData = await ipcRenderer.invoke('database-all', `
            SELECT 
                strftime('%Y-%m', created_at) as month,
                SUM(final_cost) as revenue
            FROM devices 
            WHERE status = 'delivered' 
                AND created_at >= date('now', '-6 months')
            GROUP BY strftime('%Y-%m', created_at)
            ORDER BY month
        `);
        
        // توزيع أنواع الأجهزة
        const deviceTypes = await ipcRenderer.invoke('database-all', `
            SELECT 
                device_type,
                COUNT(*) as count
            FROM devices 
            WHERE created_at >= date('now', '-1 month')
            GROUP BY device_type
            ORDER BY count DESC
        `);
        
        reportsData = {
            monthlyRevenue: monthlyRevenue?.total || 0,
            devicesThisMonth: devicesThisMonth?.count || 0,
            newCustomers: newCustomers?.count || 0,
            stockValue: stockValue?.total || 0,
            monthlyRevenueData: monthlyRevenueData || [],
            deviceTypes: deviceTypes || []
        };
        
        updateReportsDisplay();
        
    } catch (error) {
        console.error('خطأ في تحميل بيانات التقارير:', error);
        showNotification('حدث خطأ في تحميل بيانات التقارير', 'error');
    }
}

// تحديث عرض التقارير
function updateReportsDisplay() {
    // تحديث الإحصائيات
    const totalRevenueElement = document.getElementById('totalRevenue');
    const devicesThisMonthElement = document.getElementById('devicesThisMonth');
    const newCustomersElement = document.getElementById('newCustomers');
    const stockValueElement = document.getElementById('stockValue');
    
    if (totalRevenueElement) {
        totalRevenueElement.textContent = formatCurrency(reportsData.monthlyRevenue);
    }
    
    if (devicesThisMonthElement) {
        devicesThisMonthElement.textContent = reportsData.devicesThisMonth;
    }
    
    if (newCustomersElement) {
        newCustomersElement.textContent = reportsData.newCustomers;
    }
    
    if (stockValueElement) {
        stockValueElement.textContent = formatCurrency(reportsData.stockValue);
    }
}

// معالج تغيير نوع التقرير
function handleReportTypeChange(e) {
    const reportType = e.target.value;
    
    if (reportType === 'custom') {
        showCustomDateRangeModal();
    } else {
        generateReportByType(reportType);
    }
}

// إنشاء تقرير حسب النوع
async function generateReportByType(type) {
    try {
        let dateFilter = '';
        let title = '';
        
        switch (type) {
            case 'daily':
                dateFilter = "DATE(created_at) = DATE('now')";
                title = 'التقرير اليومي';
                break;
            case 'weekly':
                dateFilter = "created_at >= date('now', '-7 days')";
                title = 'التقرير الأسبوعي';
                break;
            case 'monthly':
                dateFilter = "strftime('%Y-%m', created_at) = strftime('%Y-%m', 'now')";
                title = 'التقرير الشهري';
                break;
            case 'yearly':
                dateFilter = "strftime('%Y', created_at) = strftime('%Y', 'now')";
                title = 'التقرير السنوي';
                break;
        }
        
        const reportData = await generateReportData(dateFilter);
        showReportModal(title, reportData);
        
    } catch (error) {
        console.error('خطأ في إنشاء التقرير:', error);
        showNotification('حدث خطأ في إنشاء التقرير', 'error');
    }
}

// إنشاء بيانات التقرير
async function generateReportData(dateFilter) {
    // إجمالي الإيرادات
    const revenue = await ipcRenderer.invoke('database-get', `
        SELECT SUM(final_cost) as total 
        FROM devices 
        WHERE ${dateFilter} AND status = 'delivered'
    `);
    
    // عدد الأجهزة
    const devicesCount = await ipcRenderer.invoke('database-get', `
        SELECT COUNT(*) as count 
        FROM devices 
        WHERE ${dateFilter}
    `);
    
    // الأجهزة المُسلمة
    const deliveredDevices = await ipcRenderer.invoke('database-get', `
        SELECT COUNT(*) as count 
        FROM devices 
        WHERE ${dateFilter} AND status = 'delivered'
    `);
    
    // العملاء الجدد
    const newCustomers = await ipcRenderer.invoke('database-get', `
        SELECT COUNT(*) as count 
        FROM customers 
        WHERE ${dateFilter}
    `);
    
    // أكثر أنواع الأجهزة
    const topDeviceTypes = await ipcRenderer.invoke('database-all', `
        SELECT device_type, COUNT(*) as count
        FROM devices 
        WHERE ${dateFilter}
        GROUP BY device_type
        ORDER BY count DESC
        LIMIT 5
    `);
    
    // أكثر العملاء نشاطاً
    const topCustomers = await ipcRenderer.invoke('database-all', `
        SELECT c.name, COUNT(d.id) as devices_count, SUM(d.final_cost) as total_spent
        FROM customers c
        LEFT JOIN devices d ON c.id = d.customer_id
        WHERE d.created_at IS NULL OR ${dateFilter.replace('created_at', 'd.created_at')}
        GROUP BY c.id
        ORDER BY devices_count DESC, total_spent DESC
        LIMIT 5
    `);
    
    return {
        revenue: revenue?.total || 0,
        devicesCount: devicesCount?.count || 0,
        deliveredDevices: deliveredDevices?.count || 0,
        newCustomers: newCustomers?.count || 0,
        topDeviceTypes: topDeviceTypes || [],
        topCustomers: topCustomers || []
    };
}

// إظهار نافذة التقرير
function showReportModal(title, data) {
    const modal = document.getElementById('reportModal');
    const content = document.getElementById('reportContent');
    const reportTitle = document.getElementById('reportTitle');
    
    if (!modal) {
        createReportModal();
        return showReportModal(title, data);
    }
    
    reportTitle.textContent = title;
    
    content.innerHTML = `
        <div class="report-summary">
            <div class="summary-grid">
                <div class="summary-item">
                    <h4>إجمالي الإيرادات</h4>
                    <span class="summary-value">${formatCurrency(data.revenue)}</span>
                </div>
                <div class="summary-item">
                    <h4>عدد الأجهزة</h4>
                    <span class="summary-value">${data.devicesCount}</span>
                </div>
                <div class="summary-item">
                    <h4>الأجهزة المُسلمة</h4>
                    <span class="summary-value">${data.deliveredDevices}</span>
                </div>
                <div class="summary-item">
                    <h4>العملاء الجدد</h4>
                    <span class="summary-value">${data.newCustomers}</span>
                </div>
            </div>
        </div>
        
        <div class="report-details">
            <div class="detail-section">
                <h4>أكثر أنواع الأجهزة</h4>
                ${data.topDeviceTypes.length > 0 ? createDeviceTypesTable(data.topDeviceTypes) : '<p>لا توجد بيانات</p>'}
            </div>
            
            <div class="detail-section">
                <h4>أكثر العملاء نشاطاً</h4>
                ${data.topCustomers.length > 0 ? createCustomersTable(data.topCustomers) : '<p>لا توجد بيانات</p>'}
            </div>
        </div>
    `;
    
    modal.classList.add('show');
}

// إنشاء نافذة التقرير
function createReportModal() {
    const modalHTML = `
        <div class="modal" id="reportModal">
            <div class="modal-content large">
                <div class="modal-header">
                    <h2 id="reportTitle">تقرير</h2>
                    <button class="modal-close" onclick="hideReportModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body" id="reportContent">
                    <!-- سيتم تحميل المحتوى ديناميكياً -->
                </div>
                <div class="modal-footer">
                    <button class="btn btn-outline" onclick="hideReportModal()">إغلاق</button>
                    <button class="btn btn-primary" onclick="printReport()">
                        <i class="fas fa-print"></i>
                        طباعة
                    </button>
                </div>
            </div>
        </div>
    `;
    
    document.body.insertAdjacentHTML('beforeend', modalHTML);
}

// إنشاء جدول أنواع الأجهزة
function createDeviceTypesTable(data) {
    let table = `
        <table class="table">
            <thead>
                <tr>
                    <th>نوع الجهاز</th>
                    <th>العدد</th>
                </tr>
            </thead>
            <tbody>
    `;
    
    data.forEach(item => {
        table += `
            <tr>
                <td>${item.device_type}</td>
                <td>${item.count}</td>
            </tr>
        `;
    });
    
    table += '</tbody></table>';
    return table;
}

// إنشاء جدول العملاء
function createCustomersTable(data) {
    let table = `
        <table class="table">
            <thead>
                <tr>
                    <th>اسم العميل</th>
                    <th>عدد الأجهزة</th>
                    <th>إجمالي الإنفاق</th>
                </tr>
            </thead>
            <tbody>
    `;
    
    data.forEach(item => {
        table += `
            <tr>
                <td>${item.name}</td>
                <td>${item.devices_count || 0}</td>
                <td>${formatCurrency(item.total_spent || 0)}</td>
            </tr>
        `;
    });
    
    table += '</tbody></table>';
    return table;
}

// إخفاء نافذة التقرير
function hideReportModal() {
    const modal = document.getElementById('reportModal');
    if (modal) {
        modal.classList.remove('show');
    }
}

// طباعة التقرير
function printReport() {
    window.print();
}

// إنشاء تقرير عام
function generateReport() {
    const reportType = document.getElementById('reportType').value;
    generateReportByType(reportType);
}

// عرض تقارير محددة
function showRevenueReport() {
    generateReportByType('monthly');
}

function showDevicesReport() {
    generateReportByType('monthly');
}

function showCustomersReport() {
    generateReportByType('monthly');
}

function showStockReport() {
    alert('تقرير المخزون قيد التطوير');
}

// تحديث الرسم البياني
function updateRevenueChart() {
    // سيتم تطوير هذا لاحقاً مع مكتبة الرسوم البيانية
    console.log('تحديث الرسم البياني');
}

// إظهار نافذة التاريخ المخصص
function showCustomDateRangeModal() {
    alert('اختيار فترة مخصصة قيد التطوير');
}

// دوال مساعدة
function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-SA', {
        style: 'decimal',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(amount || 0) + ' ريال';
}

function showNotification(message, type = 'info') {
    if (type === 'error') {
        alert('خطأ: ' + message);
    } else if (type === 'success') {
        alert('نجح: ' + message);
    } else {
        alert(message);
    }
}

// تصدير الدوال
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        initializeReports,
        generateReport,
        hideReportModal
    };
}
