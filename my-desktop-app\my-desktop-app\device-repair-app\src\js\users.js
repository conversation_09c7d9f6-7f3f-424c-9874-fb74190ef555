const { ipc<PERSON>enderer } = require('electron');
const bcrypt = require('bcrypt');

// متغيرات عامة
let users = [];
let currentEditUser = null;
let isEditMode = false;

// تهيئة صفحة المستخدمين
function initializeUsers() {
    // التحقق من صلاحيات المدير
    const currentUser = getCurrentUser();
    if (!currentUser || currentUser.role !== 'admin') {
        showNotification('ليس لديك صلاحية للوصول إلى هذا القسم', 'error');
        showSection('dashboard');
        return;
    }
    
    loadUsers();
    setupUsersEventListeners();
}

// إعداد مستمعي الأحداث
function setupUsersEventListeners() {
    // نموذج إضافة/تعديل المستخدم
    const userForm = document.getElementById('userForm');
    if (userForm) {
        userForm.addEventListener('submit', handleUserSubmit);
    }
    
    // البحث
    const searchInput = document.getElementById('userSearch');
    if (searchInput) {
        searchInput.addEventListener('input', handleUserSearch);
    }
    
    // فلترة حسب الدور
    const roleFilter = document.getElementById('roleFilter');
    if (roleFilter) {
        roleFilter.addEventListener('change', handleRoleFilter);
    }
}

// تحميل المستخدمين
async function loadUsers() {
    try {
        users = await ipcRenderer.invoke('database-all', 
            'SELECT * FROM users ORDER BY created_at DESC'
        );
        
        displayUsers(users);
        updateUsersStats();
        
    } catch (error) {
        console.error('خطأ في تحميل المستخدمين:', error);
        showNotification('حدث خطأ في تحميل المستخدمين', 'error');
    }
}

// عرض المستخدمين
function displayUsers(usersToShow) {
    const container = document.getElementById('usersContainer');
    if (!container) return;
    
    container.innerHTML = '';
    
    if (usersToShow.length === 0) {
        container.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-users"></i>
                <h3>لا يوجد مستخدمين</h3>
                <p>ابدأ بإضافة مستخدم جديد</p>
                <button class="btn btn-primary" onclick="showAddUserModal()">
                    <i class="fas fa-plus"></i>
                    إضافة مستخدم جديد
                </button>
            </div>
        `;
        return;
    }
    
    usersToShow.forEach(user => {
        const userCard = createUserCard(user);
        container.appendChild(userCard);
    });
}

// إنشاء بطاقة مستخدم
function createUserCard(user) {
    const div = document.createElement('div');
    div.className = 'user-card';
    
    const roleClass = getRoleClass(user.role);
    const roleName = getRoleDisplayName(user.role);
    const statusClass = user.is_active ? 'status-active' : 'status-inactive';
    const statusName = user.is_active ? 'نشط' : 'غير نشط';
    
    div.innerHTML = `
        <div class="user-header">
            <div class="user-avatar ${roleClass}">
                <i class="fas ${getRoleIcon(user.role)}"></i>
            </div>
            <div class="user-info">
                <h3>${user.full_name}</h3>
                <p><i class="fas fa-user"></i> ${user.username}</p>
                ${user.email ? `<p><i class="fas fa-envelope"></i> ${user.email}</p>` : ''}
                ${user.phone ? `<p><i class="fas fa-phone"></i> ${user.phone}</p>` : ''}
            </div>
            <div class="user-status">
                <span class="role-badge ${roleClass}">${roleName}</span>
                <span class="status-badge ${statusClass}">${statusName}</span>
            </div>
        </div>
        
        <div class="user-details">
            <div class="detail-row">
                <span class="detail-label">تاريخ الإنشاء:</span>
                <span class="detail-value">${formatDate(user.created_at)}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">آخر تحديث:</span>
                <span class="detail-value">${formatDate(user.updated_at)}</span>
            </div>
        </div>
        
        <div class="user-actions">
            <button class="btn-icon" onclick="viewUserSessions(${user.id})" title="عرض الجلسات">
                <i class="fas fa-history"></i>
            </button>
            <button class="btn-icon" onclick="editUser(${user.id})" title="تعديل">
                <i class="fas fa-edit"></i>
            </button>
            <button class="btn-icon ${user.is_active ? 'btn-warning' : 'btn-success'}" 
                    onclick="toggleUserStatus(${user.id})" 
                    title="${user.is_active ? 'تعطيل' : 'تفعيل'}">
                <i class="fas ${user.is_active ? 'fa-ban' : 'fa-check'}"></i>
            </button>
            ${user.id !== getCurrentUser().id ? `
                <button class="btn-icon btn-danger" onclick="deleteUser(${user.id})" title="حذف">
                    <i class="fas fa-trash"></i>
                </button>
            ` : ''}
        </div>
    `;
    
    return div;
}

// الحصول على فئة الدور
function getRoleClass(role) {
    const classes = {
        'admin': 'role-admin',
        'engineer': 'role-engineer',
        'receptionist': 'role-receptionist'
    };
    return classes[role] || 'role-default';
}

// الحصول على أيقونة الدور
function getRoleIcon(role) {
    const icons = {
        'admin': 'fa-user-shield',
        'engineer': 'fa-user-cog',
        'receptionist': 'fa-user-tie'
    };
    return icons[role] || 'fa-user';
}

// إظهار نافذة إضافة مستخدم
function showAddUserModal() {
    isEditMode = false;
    currentEditUser = null;
    
    const modal = document.getElementById('userModal');
    const form = document.getElementById('userForm');
    const title = document.getElementById('userModalTitle');
    const passwordGroup = document.getElementById('passwordGroup');
    
    title.textContent = 'إضافة مستخدم جديد';
    form.reset();
    
    // إظهار حقل كلمة المرور للمستخدم الجديد
    passwordGroup.style.display = 'block';
    document.getElementById('userPassword').required = true;
    
    modal.classList.add('show');
}

// تعديل المستخدم
async function editUser(userId) {
    try {
        const user = await ipcRenderer.invoke('database-get',
            'SELECT * FROM users WHERE id = ?',
            [userId]
        );
        
        if (!user) {
            showNotification('المستخدم غير موجود', 'error');
            return;
        }
        
        isEditMode = true;
        currentEditUser = user;
        
        const modal = document.getElementById('userModal');
        const form = document.getElementById('userForm');
        const title = document.getElementById('userModalTitle');
        const passwordGroup = document.getElementById('passwordGroup');
        
        title.textContent = 'تعديل بيانات المستخدم';
        
        // ملء النموذج بالبيانات الحالية
        document.getElementById('userUsername').value = user.username;
        document.getElementById('userFullName').value = user.full_name;
        document.getElementById('userRole').value = user.role;
        document.getElementById('userEmail').value = user.email || '';
        document.getElementById('userPhone').value = user.phone || '';
        document.getElementById('userActive').checked = user.is_active;
        
        // إخفاء حقل كلمة المرور في التعديل
        passwordGroup.style.display = 'none';
        document.getElementById('userPassword').required = false;
        
        modal.classList.add('show');
        
    } catch (error) {
        console.error('خطأ في تحميل بيانات المستخدم للتعديل:', error);
        showNotification('حدث خطأ في تحميل بيانات المستخدم', 'error');
    }
}

// تبديل حالة المستخدم
async function toggleUserStatus(userId) {
    try {
        const user = users.find(u => u.id === userId);
        if (!user) return;
        
        const newStatus = user.is_active ? 0 : 1;
        const action = newStatus ? 'تفعيل' : 'تعطيل';
        
        if (!confirm(`هل أنت متأكد من ${action} هذا المستخدم؟`)) {
            return;
        }
        
        await ipcRenderer.invoke('database-run',
            'UPDATE users SET is_active = ?, updated_at = ? WHERE id = ?',
            [newStatus, new Date().toISOString(), userId]
        );
        
        showNotification(`تم ${action} المستخدم بنجاح`, 'success');
        loadUsers();
        
    } catch (error) {
        console.error('خطأ في تغيير حالة المستخدم:', error);
        showNotification('حدث خطأ في تغيير حالة المستخدم', 'error');
    }
}

// حذف المستخدم
async function deleteUser(userId) {
    if (!confirm('هل أنت متأكد من حذف هذا المستخدم؟ لا يمكن التراجع عن هذا الإجراء.')) {
        return;
    }
    
    try {
        // التحقق من عدم حذف المستخدم الحالي
        if (userId === getCurrentUser().id) {
            showNotification('لا يمكنك حذف حسابك الخاص', 'error');
            return;
        }
        
        // حذف المستخدم
        await ipcRenderer.invoke('database-run',
            'DELETE FROM users WHERE id = ?',
            [userId]
        );
        
        showNotification('تم حذف المستخدم بنجاح', 'success');
        loadUsers();
        
    } catch (error) {
        console.error('خطأ في حذف المستخدم:', error);
        showNotification('حدث خطأ في حذف المستخدم', 'error');
    }
}

// معالج إرسال نموذج المستخدم
async function handleUserSubmit(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const userData = {
        username: formData.get('username').trim(),
        full_name: formData.get('full_name').trim(),
        role: formData.get('role'),
        email: formData.get('email').trim(),
        phone: formData.get('phone').trim(),
        is_active: formData.get('is_active') ? 1 : 0
    };
    
    // إضافة كلمة المرور للمستخدم الجديد
    if (!isEditMode) {
        userData.password = formData.get('password');
    }
    
    // التحقق من صحة البيانات
    if (!validateUserData(userData)) {
        return;
    }
    
    try {
        if (isEditMode && currentEditUser) {
            // تحديث المستخدم
            await ipcRenderer.invoke('database-run',
                'UPDATE users SET username = ?, full_name = ?, role = ?, email = ?, phone = ?, is_active = ?, updated_at = ? WHERE id = ?',
                [userData.username, userData.full_name, userData.role, userData.email, userData.phone, userData.is_active, new Date().toISOString(), currentEditUser.id]
            );
            
            showNotification('تم تحديث بيانات المستخدم بنجاح', 'success');
        } else {
            // تشفير كلمة المرور
            const hashedPassword = await bcrypt.hash(userData.password, 10);
            
            // إضافة مستخدم جديد
            await ipcRenderer.invoke('database-run',
                'INSERT INTO users (username, password, full_name, role, email, phone, is_active) VALUES (?, ?, ?, ?, ?, ?, ?)',
                [userData.username, hashedPassword, userData.full_name, userData.role, userData.email, userData.phone, userData.is_active]
            );
            
            showNotification('تم إضافة المستخدم بنجاح', 'success');
        }
        
        hideUserModal();
        loadUsers();
        
    } catch (error) {
        console.error('خطأ في حفظ بيانات المستخدم:', error);
        if (error.message.includes('UNIQUE constraint failed')) {
            showNotification('اسم المستخدم موجود بالفعل', 'error');
        } else {
            showNotification('حدث خطأ في حفظ بيانات المستخدم', 'error');
        }
    }
}

// التحقق من صحة بيانات المستخدم
function validateUserData(data) {
    if (!data.username) {
        showNotification('يرجى إدخال اسم المستخدم', 'error');
        return false;
    }
    
    if (data.username.length < 3) {
        showNotification('اسم المستخدم يجب أن يكون 3 أحرف على الأقل', 'error');
        return false;
    }
    
    if (!data.full_name) {
        showNotification('يرجى إدخال الاسم الكامل', 'error');
        return false;
    }
    
    if (!data.role) {
        showNotification('يرجى اختيار الدور', 'error');
        return false;
    }
    
    if (!isEditMode && (!data.password || data.password.length < 6)) {
        showNotification('كلمة المرور يجب أن تكون 6 أحرف على الأقل', 'error');
        return false;
    }
    
    // التحقق من صحة البريد الإلكتروني إذا تم إدخاله
    if (data.email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(data.email)) {
            showNotification('البريد الإلكتروني غير صحيح', 'error');
            return false;
        }
    }
    
    return true;
}

// البحث في المستخدمين
function handleUserSearch(e) {
    const searchTerm = e.target.value.toLowerCase().trim();
    
    if (!searchTerm) {
        displayUsers(users);
        return;
    }
    
    const filteredUsers = users.filter(user => 
        user.username.toLowerCase().includes(searchTerm) ||
        user.full_name.toLowerCase().includes(searchTerm) ||
        (user.email && user.email.toLowerCase().includes(searchTerm))
    );
    
    displayUsers(filteredUsers);
}

// فلترة حسب الدور
function handleRoleFilter(e) {
    const role = e.target.value;
    
    if (!role) {
        displayUsers(users);
        return;
    }
    
    const filteredUsers = users.filter(user => user.role === role);
    displayUsers(filteredUsers);
}

// تحديث إحصائيات المستخدمين
function updateUsersStats() {
    const totalUsers = users.length;
    const activeUsers = users.filter(u => u.is_active).length;
    const adminUsers = users.filter(u => u.role === 'admin').length;
    const engineerUsers = users.filter(u => u.role === 'engineer').length;
    
    // تحديث العناصر في الواجهة
    const totalElement = document.getElementById('totalUsersCount');
    const activeElement = document.getElementById('activeUsersCount');
    const adminElement = document.getElementById('adminUsersCount');
    const engineerElement = document.getElementById('engineerUsersCount');
    
    if (totalElement) totalElement.textContent = totalUsers;
    if (activeElement) activeElement.textContent = activeUsers;
    if (adminElement) adminElement.textContent = adminUsers;
    if (engineerElement) engineerElement.textContent = engineerUsers;
}

// عرض جلسات المستخدم
async function viewUserSessions(userId) {
    try {
        const sessions = await ipcRenderer.invoke('database-all',
            'SELECT * FROM user_sessions WHERE user_id = ? ORDER BY login_time DESC LIMIT 10',
            [userId]
        );
        
        const user = users.find(u => u.id === userId);
        showUserSessionsModal(user, sessions);
        
    } catch (error) {
        console.error('خطأ في تحميل جلسات المستخدم:', error);
        showNotification('حدث خطأ في تحميل جلسات المستخدم', 'error');
    }
}

// إظهار نافذة جلسات المستخدم
function showUserSessionsModal(user, sessions) {
    const modal = document.getElementById('userSessionsModal');
    const content = document.getElementById('userSessionsContent');
    
    let sessionsHtml = '';
    if (sessions.length > 0) {
        sessionsHtml = `
            <table class="table">
                <thead>
                    <tr>
                        <th>وقت الدخول</th>
                        <th>وقت الخروج</th>
                        <th>المدة</th>
                    </tr>
                </thead>
                <tbody>
        `;
        
        sessions.forEach(session => {
            const loginTime = new Date(session.login_time);
            const logoutTime = session.logout_time ? new Date(session.logout_time) : null;
            const duration = logoutTime ? 
                Math.round((logoutTime - loginTime) / (1000 * 60)) + ' دقيقة' : 
                'جلسة نشطة';
            
            sessionsHtml += `
                <tr>
                    <td>${formatDateTime(session.login_time)}</td>
                    <td>${session.logout_time ? formatDateTime(session.logout_time) : '-'}</td>
                    <td>${duration}</td>
                </tr>
            `;
        });
        
        sessionsHtml += '</tbody></table>';
    } else {
        sessionsHtml = '<p class="text-muted">لا توجد جلسات مسجلة</p>';
    }
    
    content.innerHTML = `
        <div class="user-sessions-info">
            <h3>${user.full_name} (${user.username})</h3>
            <p>آخر 10 جلسات دخول</p>
        </div>
        ${sessionsHtml}
    `;
    
    modal.classList.add('show');
}

// إخفاء نافذة المستخدم
function hideUserModal() {
    const modal = document.getElementById('userModal');
    modal.classList.remove('show');
}

// إخفاء نافذة جلسات المستخدم
function hideUserSessionsModal() {
    const modal = document.getElementById('userSessionsModal');
    modal.classList.remove('show');
}

// دوال مساعدة
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA');
}

function formatDateTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleString('ar-SA');
}

function getRoleDisplayName(role) {
    const roles = {
        'admin': 'مدير',
        'engineer': 'مهندس',
        'receptionist': 'موظف استقبال'
    };
    return roles[role] || role;
}

function showNotification(message, type = 'info') {
    if (type === 'error') {
        alert('خطأ: ' + message);
    } else if (type === 'success') {
        alert('نجح: ' + message);
    } else {
        alert(message);
    }
}

// تصدير الدوال
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        initializeUsers,
        showAddUserModal,
        hideUserModal,
        hideUserSessionsModal
    };
}
