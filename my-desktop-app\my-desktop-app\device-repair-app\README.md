# نظام إدارة محلات صيانة الأجهزة الإلكترونية

## 🎯 نظرة عامة

نظام شامل ومتطور لإدارة محلات صيانة الأجهزة الإلكترونية، مصمم خصيصاً للسوق العربي بواجهة عربية كاملة وخط Cairo الأنيق.

## ✨ المميزات الرئيسية

### 🔐 نظام المصادقة والصلاحيات
- تسجيل دخول آمن مع تشفير كلمات المرور
- ثلاثة أدوار: مدير، مهندس، موظف استقبال
- نظام صلاحيات متقدم
- تتبع جلسات المستخدمين

### 👥 إدارة العملاء
- إضافة وتعديل وحذف العملاء
- تتبع سجل الأجهزة لكل عميل
- إحصائيات العملاء والإنفاق
- البحث والفلترة المتقدمة

### 📱 إدارة الأجهزة
- استلام الأجهزة مع جميع التفاصيل
- تتبع حالة الإصلاح (مستلم، قيد الفحص، قيد الإصلاح، مكتمل، مُسلم)
- ربط الأجهزة بالعملاء والمهندسين
- حساب التكاليف والرسوم
- طباعة إيصالات الاستلام

### 🔧 إدارة قطع الغيار
- نظام مخزون ذكي
- تنبيهات نفاد المخزون
- تتبع أسعار الشراء والبيع
- ربط القطع بالإصلاحات
- إدارة الموردين

### 👨‍💼 إدارة المستخدمين (للمدير فقط)
- إضافة وتعديل المستخدمين
- تحديد الأدوار والصلاحيات
- تفعيل/تعطيل المستخدمين
- عرض سجل الجلسات

### 📊 التقارير والإحصائيات
- تقارير الإيرادات والأرباح
- إحصائيات الأجهزة والعملاء
- تقارير المخزون
- تقارير أداء المهندسين
- تصدير التقارير

### ⚙️ الإعدادات
- إدارة الملف الشخصي
- تغيير كلمة المرور
- إعدادات النظام
- النسخ الاحتياطي واستعادة البيانات
- الوضع المظلم

## 🚀 التشغيل

### المتطلبات
- Node.js (الإصدار 14 أو أحدث)
- npm أو yarn

### التثبيت
```bash
# تثبيت التبعيات
npm install

# تشغيل التطبيق
npm start

# تشغيل في وضع التطوير
npm run dev
```

### بناء التطبيق
```bash
# بناء التطبيق للتوزيع
npm run build
```

## 🔑 بيانات الدخول الافتراضية

### المدير
- **اسم المستخدم:** `admin`
- **كلمة المرور:** `admin123`

### المهندس (بيانات تجريبية)
- **اسم المستخدم:** `eng_omar`
- **كلمة المرور:** `engineer123`

### موظف الاستقبال (بيانات تجريبية)
- **اسم المستخدم:** `receptionist`
- **كلمة المرور:** `reception123`

## 📁 هيكل المشروع

```
device-repair-app/
├── main.js                 # الملف الرئيسي لـ Electron
├── package.json            # تبعيات المشروع
├── src/
│   ├── views/
│   │   ├── login.html      # صفحة تسجيل الدخول
│   │   └── dashboard.html  # لوحة التحكم الرئيسية
│   ├── css/
│   │   ├── main.css        # الأنماط الأساسية
│   │   ├── login.css       # أنماط تسجيل الدخول
│   │   └── dashboard.css   # أنماط لوحة التحكم
│   ├── js/
│   │   ├── login.js        # منطق تسجيل الدخول
│   │   ├── dashboard.js    # منطق لوحة التحكم
│   │   ├── customers.js    # إدارة العملاء
│   │   ├── devices.js      # إدارة الأجهزة
│   │   ├── users.js        # إدارة المستخدمين
│   │   ├── spare-parts.js  # إدارة قطع الغيار
│   │   ├── reports.js      # التقارير
│   │   ├── settings.js     # الإعدادات
│   │   └── sample-data.js  # البيانات التجريبية
│   ├── database/
│   │   └── database.js     # إدارة قاعدة البيانات
│   └── assets/             # الصور والملفات الثابتة
└── data/
    └── repair_shop.db      # قاعدة بيانات SQLite
```

## 🗄️ قاعدة البيانات

النظام يستخدم قاعدة بيانات SQLite مع الجداول التالية:

- **users** - المستخدمين والصلاحيات
- **customers** - بيانات العملاء
- **devices** - الأجهزة والإصلاحات
- **spare_parts** - قطع الغيار والمخزون
- **device_spare_parts** - ربط القطع بالإصلاحات
- **device_status_log** - سجل تغيير حالات الأجهزة
- **payments** - المدفوعات
- **user_sessions** - جلسات المستخدمين

## 🎨 التصميم

- **الخط:** Cairo من Google Fonts
- **الاتجاه:** من اليمين إلى اليسار (RTL)
- **الألوان:** نظام ألوان احترافي مع متغيرات CSS
- **التجاوب:** تصميم متجاوب يعمل على جميع الأحجام
- **الرسوم المتحركة:** انتقالات سلسة وتأثيرات بصرية

## 🔧 التقنيات المستخدمة

- **Electron** - إطار العمل الرئيسي
- **Node.js** - بيئة التشغيل
- **SQLite** - قاعدة البيانات
- **HTML5/CSS3** - الواجهة الأمامية
- **JavaScript ES6+** - المنطق البرمجي
- **bcrypt** - تشفير كلمات المرور
- **Font Awesome** - الأيقونات

## 📝 الاستخدام

### 1. تسجيل الدخول
- استخدم بيانات الدخول الافتراضية
- أو قم بإنشاء مستخدمين جدد من قسم إدارة المستخدمين

### 2. إضافة البيانات التجريبية
- اضغط على زر "بيانات تجريبية" في لوحة التحكم
- سيتم إضافة عملاء ومهندسين وأجهزة وقطع غيار تجريبية

### 3. إدارة العملاء
- انتقل إلى قسم "إدارة العملاء"
- أضف عملاء جدد أو عدل البيانات الموجودة
- اعرض سجل الأجهزة لكل عميل

### 4. استلام الأجهزة
- انتقل إلى قسم "إدارة الأجهزة"
- اضغط "إضافة جهاز جديد"
- املأ جميع البيانات المطلوبة
- اطبع إيصال الاستلام

### 5. إدارة المخزون
- انتقل إلى قسم "قطع الغيار"
- أضف قطع غيار جديدة
- راقب مستويات المخزون
- احصل على تنبيهات النفاد

### 6. التقارير
- انتقل إلى قسم "التقارير"
- اختر نوع التقرير المطلوب
- اعرض الإحصائيات والرسوم البيانية
- اطبع أو صدر التقارير

## 🔒 الأمان

- تشفير كلمات المرور باستخدام bcrypt
- نظام صلاحيات متدرج
- تتبع جلسات المستخدمين
- حماية من SQL Injection
- التحقق من صحة البيانات

## 🚧 التطوير المستقبلي

- [ ] إضافة مكتبة رسوم بيانية (Chart.js)
- [ ] نظام إشعارات متقدم
- [ ] تصدير البيانات إلى Excel
- [ ] نظام النسخ الاحتياطي التلقائي
- [ ] واجهة برمجة تطبيقات (API)
- [ ] تطبيق جوال مصاحب
- [ ] نظام الباركود
- [ ] تكامل مع أنظمة الدفع

## 📞 الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى التواصل معنا.

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف [LICENSE](LICENSE) للتفاصيل.

---

**تم تطوير هذا النظام بعناية فائقة ليلبي احتياجات محلات صيانة الأجهزة الإلكترونية في المنطقة العربية.**
