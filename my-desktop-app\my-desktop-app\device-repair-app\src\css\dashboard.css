/* لوحة التحكم */
.dashboard-body {
    background: #f8fafc;
    margin: 0;
    padding: 0;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* الشريط العلوي */
.top-header {
    background: white;
    height: 70px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 20px;
}

.menu-toggle {
    background: none;
    border: none;
    font-size: 20px;
    color: var(--dark-color);
    cursor: pointer;
    padding: 10px;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.menu-toggle:hover {
    background: var(--light-color);
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 700;
    font-size: 18px;
    color: var(--primary-color);
}

.logo-section i {
    font-size: 24px;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

/* الإشعارات */
.notifications {
    position: relative;
}

.notification-btn {
    background: none;
    border: none;
    font-size: 20px;
    color: var(--dark-color);
    cursor: pointer;
    padding: 10px;
    border-radius: 8px;
    position: relative;
    transition: all 0.3s ease;
}

.notification-btn:hover {
    background: var(--light-color);
}

.notification-badge {
    position: absolute;
    top: 5px;
    right: 5px;
    background: var(--danger-color);
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 16px;
    text-align: center;
}

/* قائمة المستخدم */
.user-menu {
    position: relative;
}

.user-btn {
    background: none;
    border: none;
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.user-btn:hover {
    background: var(--light-color);
}

.user-avatar {
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
}

.user-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    text-align: right;
}

.user-name {
    font-weight: 600;
    color: var(--dark-color);
    font-size: 14px;
}

.user-role {
    font-size: 12px;
    color: #6b7280;
}

.user-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border-radius: 8px;
    box-shadow: var(--shadow-lg);
    min-width: 200px;
    padding: 8px 0;
    display: none;
    z-index: 1001;
}

.user-dropdown.show {
    display: block;
    animation: fadeIn 0.2s ease-out;
}

.user-dropdown a {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 16px;
    text-decoration: none;
    color: var(--dark-color);
    transition: all 0.3s ease;
}

.user-dropdown a:hover {
    background: var(--light-color);
}

.dropdown-divider {
    height: 1px;
    background: var(--border-color);
    margin: 8px 0;
}

/* الحاوي الرئيسي */
.main-container {
    display: flex;
    margin-top: 70px;
    min-height: calc(100vh - 70px);
}

/* القائمة الجانبية */
.sidebar {
    width: 280px;
    background: white;
    box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: fixed;
    top: 70px;
    bottom: 0;
    right: 0;
    z-index: 999;
    overflow-y: auto;
}

.sidebar.collapsed {
    width: 70px;
}

.sidebar-nav {
    padding: 20px 0;
}

.nav-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.nav-item {
    margin-bottom: 5px;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px 20px;
    text-decoration: none;
    color: var(--dark-color);
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover {
    background: rgba(79, 70, 229, 0.1);
    color: var(--primary-color);
}

.nav-item.active .nav-link {
    background: var(--primary-color);
    color: white;
}

.nav-link i {
    font-size: 18px;
    min-width: 20px;
}

.nav-badge {
    background: var(--primary-color);
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
    margin-right: auto;
}

.nav-badge.warning {
    background: var(--warning-color);
}

/* المحتوى الرئيسي */
.main-content {
    flex: 1;
    margin-right: 280px;
    padding: 30px;
    transition: all 0.3s ease;
}

.sidebar.collapsed + .main-content {
    margin-right: 70px;
}

/* أقسام المحتوى */
.content-section {
    display: none;
}

.content-section.active {
    display: block;
    animation: fadeIn 0.3s ease-out;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid var(--border-color);
}

.section-header h1 {
    font-size: 28px;
    font-weight: 700;
    color: var(--dark-color);
    margin: 0;
}

.section-header p {
    color: #6b7280;
    margin: 5px 0 0 0;
}

/* البطاقات الإحصائية */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: var(--shadow);
    display: flex;
    align-items: center;
    gap: 20px;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
}

.stat-content h3 {
    font-size: 28px;
    font-weight: 700;
    color: var(--dark-color);
    margin: 0 0 5px 0;
}

.stat-content p {
    color: #6b7280;
    margin: 0;
    font-size: 14px;
}

/* شبكة لوحة التحكم */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
}

.dashboard-card {
    background: white;
    border-radius: 12px;
    box-shadow: var(--shadow);
    overflow: hidden;
}

.dashboard-card .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
}

.dashboard-card .card-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--dark-color);
    margin: 0;
}

/* لوحة الإشعارات */
.notification-panel {
    position: fixed;
    top: 70px;
    left: 20px;
    width: 350px;
    background: white;
    border-radius: 12px;
    box-shadow: var(--shadow-lg);
    z-index: 1002;
    display: none;
    max-height: 500px;
    overflow: hidden;
}

.notification-panel.show {
    display: block;
    animation: slideIn 0.3s ease-out;
}

.notification-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
}

.notification-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--dark-color);
    margin: 0;
}

.notification-header button {
    background: none;
    border: none;
    font-size: 16px;
    color: #6b7280;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.notification-header button:hover {
    background: var(--light-color);
}

.notification-list {
    max-height: 400px;
    overflow-y: auto;
}

.notification-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.notification-item:hover {
    background: var(--light-color);
}

.notification-item.unread {
    background: rgba(79, 70, 229, 0.05);
}

.notification-item i {
    font-size: 16px;
    margin-top: 2px;
}

.notification-item p {
    margin: 0 0 5px 0;
    font-size: 14px;
    color: var(--dark-color);
    line-height: 1.4;
}

.notification-item small {
    color: #6b7280;
    font-size: 12px;
}

/* إخفاء العناصر الخاصة بالمدير */
.admin-only {
    display: none;
}

body.admin .admin-only {
    display: block;
}

/* أنماط العملاء */
.header-actions {
    display: flex;
    align-items: center;
    gap: 20px;
}

.search-box {
    position: relative;
    display: flex;
    align-items: center;
}

.search-box i {
    position: absolute;
    right: 15px;
    color: #6b7280;
    z-index: 1;
}

.search-box input {
    padding-right: 45px;
    min-width: 300px;
}

.customers-stats {
    display: flex;
    gap: 20px;
    margin-bottom: 30px;
}

.customers-stats .stat-item {
    display: flex;
    align-items: center;
    gap: 15px;
    background: white;
    padding: 20px;
    border-radius: 12px;
    box-shadow: var(--shadow);
}

.customers-stats .stat-item i {
    font-size: 24px;
    color: var(--primary-color);
}

.customers-stats .stat-number {
    font-size: 24px;
    font-weight: 700;
    color: var(--dark-color);
    display: block;
}

.customers-stats .stat-label {
    font-size: 14px;
    color: #6b7280;
}

.customers-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 20px;
}

.customer-card {
    background: white;
    border-radius: 12px;
    box-shadow: var(--shadow);
    overflow: hidden;
    transition: all 0.3s ease;
}

.customer-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.customer-header {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
}

.customer-avatar {
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
}

.customer-info {
    flex: 1;
}

.customer-info h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--dark-color);
    margin: 0 0 5px 0;
}

.customer-info p {
    margin: 2px 0;
    font-size: 14px;
    color: #6b7280;
    display: flex;
    align-items: center;
    gap: 8px;
}

.customer-info i {
    width: 16px;
    color: var(--primary-color);
}

.customer-actions {
    display: flex;
    gap: 5px;
}

.btn-icon {
    width: 35px;
    height: 35px;
    border: none;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: var(--light-color);
    color: var(--dark-color);
}

.btn-icon:hover {
    background: var(--primary-color);
    color: white;
}

.btn-icon.btn-danger:hover {
    background: var(--danger-color);
}

.customer-details {
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
}

.customer-details p {
    margin: 5px 0;
    font-size: 14px;
    color: #6b7280;
    display: flex;
    align-items: center;
    gap: 8px;
}

.customer-details i {
    width: 16px;
    color: var(--primary-color);
}

.customer-stats {
    display: flex;
    padding: 15px 20px;
}

.customer-stats .stat-item {
    flex: 1;
    text-align: center;
}

.customer-stats .stat-number {
    display: block;
    font-size: 18px;
    font-weight: 700;
    color: var(--primary-color);
}

.customer-stats .stat-label {
    font-size: 12px;
    color: #6b7280;
    margin-top: 2px;
}

.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #6b7280;
}

.empty-state i {
    font-size: 64px;
    color: #d1d5db;
    margin-bottom: 20px;
}

.empty-state h3 {
    font-size: 24px;
    margin-bottom: 10px;
    color: var(--dark-color);
}

.empty-state p {
    font-size: 16px;
    margin-bottom: 30px;
}

/* النوافذ المنبثقة */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    backdrop-filter: blur(5px);
}

.modal.show {
    display: flex;
    animation: fadeIn 0.3s ease-out;
}

.modal-content {
    background: white;
    border-radius: 12px;
    box-shadow: var(--shadow-lg);
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    overflow: hidden;
    animation: slideInUp 0.3s ease-out;
}

.modal-content.large {
    max-width: 900px;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h2 {
    font-size: 20px;
    font-weight: 600;
    color: var(--dark-color);
    margin: 0;
}

.modal-close {
    width: 35px;
    height: 35px;
    border: none;
    border-radius: 8px;
    background: var(--light-color);
    color: var(--dark-color);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.modal-close:hover {
    background: var(--danger-color);
    color: white;
}

.modal-body {
    padding: 20px;
    max-height: calc(90vh - 140px);
    overflow-y: auto;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding: 20px;
    border-top: 1px solid var(--border-color);
    margin-top: 20px;
}

.form-row {
    display: flex;
    gap: 20px;
}

.form-row .form-group {
    flex: 1;
}

/* ملف العميل */
.customer-profile {
    padding: 0;
}

.profile-header {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 20px;
    background: var(--light-color);
    border-radius: 8px;
    margin-bottom: 30px;
}

.profile-avatar {
    width: 80px;
    height: 80px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 32px;
}

.profile-info h2 {
    font-size: 24px;
    font-weight: 700;
    color: var(--dark-color);
    margin: 0 0 10px 0;
}

.profile-info p {
    margin: 5px 0;
    font-size: 16px;
    color: #6b7280;
    display: flex;
    align-items: center;
    gap: 10px;
}

.profile-info i {
    width: 20px;
    color: var(--primary-color);
}

.profile-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.profile-stats .stat-card {
    background: white;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    padding: 20px;
    text-align: center;
}

.profile-stats .stat-card h3 {
    font-size: 28px;
    font-weight: 700;
    color: var(--primary-color);
    margin: 0 0 5px 0;
}

.profile-stats .stat-card p {
    font-size: 14px;
    color: #6b7280;
    margin: 0;
}

.devices-history h3 {
    font-size: 20px;
    font-weight: 600;
    color: var(--dark-color);
    margin: 0 0 20px 0;
}

/* أنماط الأجهزة */
.devices-stats {
    display: flex;
    gap: 20px;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.devices-stats .stat-item {
    display: flex;
    align-items: center;
    gap: 15px;
    background: white;
    padding: 20px;
    border-radius: 12px;
    box-shadow: var(--shadow);
    flex: 1;
    min-width: 200px;
}

.devices-stats .stat-item i {
    font-size: 24px;
    color: var(--primary-color);
}

.devices-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 20px;
}

.device-card {
    background: white;
    border-radius: 12px;
    box-shadow: var(--shadow);
    overflow: hidden;
    transition: all 0.3s ease;
}

.device-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.device-header {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
}

.device-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
}

.device-info {
    flex: 1;
}

.device-info h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--dark-color);
    margin: 0 0 5px 0;
}

.device-info p {
    margin: 2px 0;
    font-size: 14px;
    color: #6b7280;
    display: flex;
    align-items: center;
    gap: 8px;
}

.device-info i {
    width: 16px;
    color: var(--primary-color);
}

.device-status {
    text-align: center;
}

.device-details {
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.detail-row:last-child {
    margin-bottom: 0;
}

.detail-label {
    font-size: 14px;
    color: #6b7280;
    font-weight: 500;
}

.detail-value {
    font-size: 14px;
    color: var(--dark-color);
    font-weight: 500;
    text-align: left;
}

.device-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
}

.device-cost {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.cost-label {
    font-size: 12px;
    color: #6b7280;
    margin-bottom: 2px;
}

.cost-value {
    font-size: 16px;
    font-weight: 700;
    color: var(--success-color);
}

.device-actions {
    display: flex;
    gap: 5px;
}

/* حالات الأجهزة */
.status-received { background: #dbeafe; color: #1e40af; }
.status-inspecting { background: #fef3c7; color: #d97706; }
.status-repairing { background: #fed7d7; color: #c53030; }
.status-completed { background: #d1fae5; color: #059669; }
.status-delivered { background: #e5e7eb; color: #374151; }
.status-cancelled { background: #fee2e2; color: #dc2626; }

/* أقسام النموذج */
.form-section {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid var(--border-color);
}

.form-section:last-of-type {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.form-section h3 {
    font-size: 16px;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.form-section h3 i {
    color: var(--primary-color);
}

.customer-info-display {
    background: var(--light-color);
    padding: 15px;
    border-radius: 8px;
    margin-top: 10px;
    display: none;
}

.customer-info-display.show {
    display: block;
}

.customer-info-display p {
    margin: 5px 0;
    font-size: 14px;
    color: var(--dark-color);
}

/* أنماط المستخدمين */
.users-stats {
    display: flex;
    gap: 20px;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.users-stats .stat-item {
    display: flex;
    align-items: center;
    gap: 15px;
    background: white;
    padding: 20px;
    border-radius: 12px;
    box-shadow: var(--shadow);
    flex: 1;
    min-width: 200px;
}

.users-stats .stat-item i {
    font-size: 24px;
    color: var(--primary-color);
}

.users-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 20px;
}

.user-card {
    background: white;
    border-radius: 12px;
    box-shadow: var(--shadow);
    overflow: hidden;
    transition: all 0.3s ease;
}

.user-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.user-header {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
}

.user-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
}

.user-avatar.role-admin {
    background: linear-gradient(135deg, #dc2626, #ef4444);
}

.user-avatar.role-engineer {
    background: linear-gradient(135deg, #2563eb, #3b82f6);
}

.user-avatar.role-receptionist {
    background: linear-gradient(135deg, #059669, #10b981);
}

.user-info {
    flex: 1;
}

.user-info h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--dark-color);
    margin: 0 0 5px 0;
}

.user-info p {
    margin: 2px 0;
    font-size: 14px;
    color: #6b7280;
    display: flex;
    align-items: center;
    gap: 8px;
}

.user-info i {
    width: 16px;
    color: var(--primary-color);
}

.user-status {
    display: flex;
    flex-direction: column;
    gap: 8px;
    align-items: flex-end;
}

.role-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
}

.role-badge.role-admin {
    background: #fee2e2;
    color: #dc2626;
}

.role-badge.role-engineer {
    background: #dbeafe;
    color: #2563eb;
}

.role-badge.role-receptionist {
    background: #d1fae5;
    color: #059669;
}

.status-active {
    background: #d1fae5;
    color: #059669;
}

.status-inactive {
    background: #fee2e2;
    color: #dc2626;
}

.user-details {
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
}

.user-actions {
    display: flex;
    justify-content: center;
    gap: 5px;
    padding: 15px 20px;
}

.btn-warning {
    background: var(--warning-color);
    color: white;
}

.btn-warning:hover {
    background: #d97706;
}

.btn-success {
    background: var(--success-color);
    color: white;
}

.btn-success:hover {
    background: #047857;
}

/* جلسات المستخدم */
.user-sessions-info {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--border-color);
}

.user-sessions-info h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--dark-color);
    margin: 0 0 5px 0;
}

.user-sessions-info p {
    color: #6b7280;
    margin: 0;
}

/* أنماط قطع الغيار */
.spare-parts-stats {
    display: flex;
    gap: 20px;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.spare-parts-stats .stat-item {
    display: flex;
    align-items: center;
    gap: 15px;
    background: white;
    padding: 20px;
    border-radius: 12px;
    box-shadow: var(--shadow);
    flex: 1;
    min-width: 200px;
}

.spare-parts-stats .stat-item i {
    font-size: 24px;
}

.spare-parts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 20px;
}

.spare-part-card {
    background: white;
    border-radius: 12px;
    box-shadow: var(--shadow);
    overflow: hidden;
    transition: all 0.3s ease;
}

.spare-part-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.spare-part-header {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
}

.spare-part-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--secondary-color), #0891b2);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
}

.spare-part-info {
    flex: 1;
}

.spare-part-info h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--dark-color);
    margin: 0 0 5px 0;
}

.spare-part-info p {
    margin: 2px 0;
    font-size: 14px;
    color: #6b7280;
    display: flex;
    align-items: center;
    gap: 8px;
}

.spare-part-info i {
    width: 16px;
    color: var(--secondary-color);
}

.stock-status {
    text-align: center;
}

.stock-badge {
    display: block;
    font-size: 24px;
    font-weight: 700;
    padding: 8px 12px;
    border-radius: 8px;
    margin-bottom: 4px;
}

.stock-badge.stock-good {
    background: #d1fae5;
    color: #059669;
}

.stock-badge.stock-low {
    background: #fef3c7;
    color: #d97706;
}

.stock-badge.stock-out {
    background: #fee2e2;
    color: #dc2626;
}

.stock-label {
    font-size: 12px;
    color: #6b7280;
}

.spare-part-details {
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
}

.spare-part-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
}

.stock-status-text {
    font-size: 14px;
    font-weight: 600;
}

.stock-status-text .stock-good {
    color: #059669;
}

.stock-status-text .stock-low {
    color: #d97706;
}

.stock-status-text .stock-out {
    color: #dc2626;
}

.spare-part-actions {
    display: flex;
    gap: 5px;
}

.profit {
    color: var(--success-color);
    font-weight: 600;
}

/* أنماط التقارير */
.reports-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.report-card {
    background: white;
    border-radius: 12px;
    box-shadow: var(--shadow);
    padding: 25px;
    display: flex;
    align-items: center;
    gap: 20px;
    transition: all 0.3s ease;
}

.report-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.report-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
}

.report-content {
    flex: 1;
}

.report-content h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--dark-color);
    margin: 0 0 5px 0;
}

.report-content p {
    font-size: 14px;
    color: #6b7280;
    margin: 0 0 10px 0;
}

.report-stats {
    display: flex;
    flex-direction: column;
}

.stat-value {
    font-size: 20px;
    font-weight: 700;
    color: var(--primary-color);
}

.stat-label {
    font-size: 12px;
    color: #6b7280;
}

.charts-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
}

.chart-card {
    background: white;
    border-radius: 12px;
    box-shadow: var(--shadow);
    padding: 20px;
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--border-color);
}

.chart-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--dark-color);
    margin: 0;
}

.chart-container {
    position: relative;
    height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--light-color);
    border-radius: 8px;
    color: #6b7280;
    font-size: 16px;
}

.chart-container::before {
    content: "الرسم البياني قيد التطوير";
}

/* أنماط الإعدادات */
.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
}

.settings-card {
    background: white;
    border-radius: 12px;
    box-shadow: var(--shadow);
    overflow: hidden;
}

.settings-header {
    background: var(--light-color);
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
}

.settings-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--dark-color);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.settings-header i {
    color: var(--primary-color);
}

.settings-content {
    padding: 20px;
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid var(--border-color);
}

.setting-item:last-child {
    border-bottom: none;
}

.setting-info h4 {
    font-size: 16px;
    font-weight: 600;
    color: var(--dark-color);
    margin: 0 0 5px 0;
}

.setting-info p {
    font-size: 14px;
    color: #6b7280;
    margin: 0;
}

/* مفتاح التبديل */
.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.4s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: var(--primary-color);
}

input:checked + .slider:before {
    transform: translateX(26px);
}

/* إجراءات البيانات */
.data-action {
    padding: 15px 0;
    border-bottom: 1px solid var(--border-color);
}

.data-action:last-child {
    border-bottom: none;
}

.data-action h4 {
    font-size: 16px;
    font-weight: 600;
    color: var(--dark-color);
    margin: 0 0 5px 0;
}

.data-action p {
    font-size: 14px;
    color: #6b7280;
    margin: 0 0 10px 0;
}

/* معلومات النظام */
.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid var(--border-color);
}

.info-item:last-child {
    border-bottom: none;
}

.info-label {
    font-size: 14px;
    color: #6b7280;
}

.info-value {
    font-size: 14px;
    font-weight: 600;
    color: var(--dark-color);
}

/* الرسوم المتحركة */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* الأقسام الفرعية */
.recent-devices, .low-stock-items {
    padding: 10px 0;
}

.device-item, .stock-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid var(--border-color);
}

.device-item:last-child, .stock-item:last-child {
    border-bottom: none;
}

.device-info, .stock-info {
    flex: 1;
}

.device-name, .stock-name {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 4px;
}

.device-status, .stock-quantity {
    font-size: 12px;
    color: #6b7280;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
}

.status-received { background: #dbeafe; color: #1e40af; }
.status-repairing { background: #fef3c7; color: #d97706; }
.status-completed { background: #d1fae5; color: #059669; }
.status-delivered { background: #e5e7eb; color: #374151; }

.stock-low { color: var(--danger-color); font-weight: 600; }
.stock-warning { color: var(--warning-color); font-weight: 600; }

/* التصميم المتجاوب */
@media (max-width: 1024px) {
    .sidebar {
        transform: translateX(100%);
    }

    .sidebar.show {
        transform: translateX(0);
    }

    .main-content {
        margin-right: 0;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }

    .dashboard-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .top-header {
        padding: 0 15px;
    }

    .header-left .logo-section span {
        display: none;
    }

    .main-content {
        padding: 20px 15px;
    }

    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .stat-card {
        padding: 20px;
    }

    .notification-panel {
        left: 10px;
        right: 10px;
        width: auto;
    }
}
