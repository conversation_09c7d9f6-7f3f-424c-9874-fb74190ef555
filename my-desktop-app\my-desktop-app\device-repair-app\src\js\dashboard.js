const { ipc<PERSON>enderer } = require('electron');

// متغيرات عامة
let currentUser = null;
let dashboardData = {};

// تهيئة لوحة التحكم
document.addEventListener('DOMContentLoaded', function() {
    initializeDashboard();
});

// تهيئة لوحة التحكم
async function initializeDashboard() {
    try {
        // التحقق من تسجيل الدخول
        currentUser = getCurrentUser();
        if (!currentUser) {
            window.location.href = 'login.html';
            return;
        }
        
        // تحديث معلومات المستخدم في الواجهة
        updateUserInfo();
        
        // تحديد الصلاحيات
        setUserPermissions();
        
        // تحميل بيانات لوحة التحكم
        await loadDashboardData();
        
        // تحديث الإحصائيات
        updateStatistics();
        
        // تحميل الأجهزة الحديثة
        loadRecentDevices();
        
        // تحميل قطع الغيار المنخفضة
        loadLowStockItems();
        
        // تحديث الإشعارات
        updateNotifications();
        
    } catch (error) {
        console.error('خطأ في تهيئة لوحة التحكم:', error);
        showError('حدث خطأ في تحميل لوحة التحكم');
    }
}

// تحديث معلومات المستخدم
function updateUserInfo() {
    if (currentUser) {
        document.getElementById('userName').textContent = currentUser.full_name;
        document.getElementById('userRole').textContent = getRoleDisplayName(currentUser.role);
    }
}

// الحصول على اسم الدور للعرض
function getRoleDisplayName(role) {
    const roles = {
        'admin': 'مدير',
        'engineer': 'مهندس',
        'receptionist': 'موظف استقبال'
    };
    return roles[role] || role;
}

// تحديد الصلاحيات
function setUserPermissions() {
    if (currentUser && currentUser.role === 'admin') {
        document.body.classList.add('admin');
    }
    
    // إخفاء/إظهار العناصر حسب الدور
    const adminElements = document.querySelectorAll('.admin-only');
    adminElements.forEach(element => {
        if (currentUser.role !== 'admin') {
            element.style.display = 'none';
        }
    });
}

// تحميل بيانات لوحة التحكم
async function loadDashboardData() {
    try {
        // إحصائيات الأجهزة
        const totalDevices = await ipcRenderer.invoke('database-get', 
            'SELECT COUNT(*) as count FROM devices'
        );
        
        const pendingDevices = await ipcRenderer.invoke('database-get',
            'SELECT COUNT(*) as count FROM devices WHERE status IN ("received", "inspecting", "repairing")'
        );
        
        const completedDevices = await ipcRenderer.invoke('database-get',
            'SELECT COUNT(*) as count FROM devices WHERE status = "completed"'
        );
        
        // إيرادات الشهر الحالي
        const currentMonth = new Date().toISOString().slice(0, 7);
        const monthlyRevenue = await ipcRenderer.invoke('database-get',
            'SELECT SUM(final_cost) as total FROM devices WHERE DATE(created_at) LIKE ? AND status = "delivered"',
            [`${currentMonth}%`]
        );
        
        dashboardData = {
            totalDevices: totalDevices?.count || 0,
            pendingDevices: pendingDevices?.count || 0,
            completedDevices: completedDevices?.count || 0,
            monthlyRevenue: monthlyRevenue?.total || 0
        };
        
    } catch (error) {
        console.error('خطأ في تحميل بيانات لوحة التحكم:', error);
    }
}

// تحديث الإحصائيات
function updateStatistics() {
    document.getElementById('totalDevices').textContent = dashboardData.totalDevices;
    document.getElementById('pendingDevices').textContent = dashboardData.pendingDevices;
    document.getElementById('completedDevices').textContent = dashboardData.completedDevices;
    document.getElementById('monthlyRevenue').textContent = formatCurrency(dashboardData.monthlyRevenue);
}

// تحميل الأجهزة الحديثة
async function loadRecentDevices() {
    try {
        const recentDevices = await ipcRenderer.invoke('database-all',
            `SELECT d.*, c.name as customer_name 
             FROM devices d 
             LEFT JOIN customers c ON d.customer_id = c.id 
             ORDER BY d.created_at DESC 
             LIMIT 5`
        );
        
        const container = document.getElementById('recentDevices');
        container.innerHTML = '';
        
        if (recentDevices && recentDevices.length > 0) {
            recentDevices.forEach(device => {
                const deviceElement = createDeviceElement(device);
                container.appendChild(deviceElement);
            });
        } else {
            container.innerHTML = '<p class="text-muted">لا توجد أجهزة حديثة</p>';
        }
        
    } catch (error) {
        console.error('خطأ في تحميل الأجهزة الحديثة:', error);
    }
}

// إنشاء عنصر جهاز
function createDeviceElement(device) {
    const div = document.createElement('div');
    div.className = 'device-item';
    
    div.innerHTML = `
        <div class="device-info">
            <div class="device-name">${device.brand} ${device.model}</div>
            <div class="device-status">العميل: ${device.customer_name || 'غير محدد'}</div>
        </div>
        <div class="status-badge status-${device.status}">
            ${getStatusDisplayName(device.status)}
        </div>
    `;
    
    return div;
}

// الحصول على اسم الحالة للعرض
function getStatusDisplayName(status) {
    const statuses = {
        'received': 'مستلم',
        'inspecting': 'قيد الفحص',
        'repairing': 'قيد الإصلاح',
        'completed': 'مكتمل',
        'delivered': 'مُسلم',
        'cancelled': 'ملغي'
    };
    return statuses[status] || status;
}

// تحميل قطع الغيار المنخفضة
async function loadLowStockItems() {
    try {
        const lowStockItems = await ipcRenderer.invoke('database-all',
            'SELECT * FROM spare_parts WHERE quantity_in_stock <= minimum_stock_level ORDER BY quantity_in_stock ASC LIMIT 5'
        );
        
        const container = document.getElementById('lowStockItems');
        container.innerHTML = '';
        
        if (lowStockItems && lowStockItems.length > 0) {
            lowStockItems.forEach(item => {
                const itemElement = createStockItemElement(item);
                container.appendChild(itemElement);
            });
        } else {
            container.innerHTML = '<p class="text-muted">جميع قطع الغيار متوفرة</p>';
        }
        
    } catch (error) {
        console.error('خطأ في تحميل قطع الغيار المنخفضة:', error);
    }
}

// إنشاء عنصر قطعة غيار
function createStockItemElement(item) {
    const div = document.createElement('div');
    div.className = 'stock-item';
    
    const stockClass = item.quantity_in_stock === 0 ? 'stock-low' : 'stock-warning';
    
    div.innerHTML = `
        <div class="stock-info">
            <div class="stock-name">${item.name}</div>
            <div class="stock-quantity">الفئة: ${item.category}</div>
        </div>
        <div class="${stockClass}">
            ${item.quantity_in_stock} قطعة
        </div>
    `;
    
    return div;
}

// تحديث الإشعارات
async function updateNotifications() {
    try {
        // حساب عدد الإشعارات (قطع الغيار المنخفضة + الأجهزة المتأخرة)
        const lowStockCount = await ipcRenderer.invoke('database-get',
            'SELECT COUNT(*) as count FROM spare_parts WHERE quantity_in_stock <= minimum_stock_level'
        );
        
        const overdueDevices = await ipcRenderer.invoke('database-get',
            'SELECT COUNT(*) as count FROM devices WHERE expected_delivery_date < DATE("now") AND status NOT IN ("delivered", "cancelled")'
        );
        
        const totalNotifications = (lowStockCount?.count || 0) + (overdueDevices?.count || 0);
        
        const badge = document.getElementById('notificationCount');
        if (totalNotifications > 0) {
            badge.textContent = totalNotifications;
            badge.style.display = 'block';
        } else {
            badge.style.display = 'none';
        }
        
    } catch (error) {
        console.error('خطأ في تحديث الإشعارات:', error);
    }
}

// تبديل القائمة الجانبية
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    sidebar.classList.toggle('collapsed');
}

// إظهار قسم معين
function showSection(sectionName) {
    // إخفاء جميع الأقسام
    const sections = document.querySelectorAll('.content-section');
    sections.forEach(section => {
        section.classList.remove('active');
    });

    // إظهار القسم المطلوب
    const targetSection = document.getElementById(`${sectionName}-section`);
    if (targetSection) {
        targetSection.classList.add('active');
    }

    // تحديث القائمة الجانبية
    const navItems = document.querySelectorAll('.nav-item');
    navItems.forEach(item => {
        item.classList.remove('active');
    });

    const activeNavItem = document.querySelector(`[onclick="showSection('${sectionName}')"]`).closest('.nav-item');
    if (activeNavItem) {
        activeNavItem.classList.add('active');
    }

    // تهيئة القسم المحدد
    if (sectionName === 'customers') {
        if (typeof initializeCustomers === 'function') {
            initializeCustomers();
        }
    } else if (sectionName === 'devices') {
        if (typeof initializeDevices === 'function') {
            initializeDevices();
        }
    } else if (sectionName === 'users') {
        if (typeof initializeUsers === 'function') {
            initializeUsers();
        }
    }

    // إخفاء القائمة الجانبية في الشاشات الصغيرة
    if (window.innerWidth <= 1024) {
        const sidebar = document.getElementById('sidebar');
        sidebar.classList.remove('show');
    }
}

// تبديل قائمة المستخدم
function toggleUserMenu() {
    const dropdown = document.getElementById('userDropdown');
    dropdown.classList.toggle('show');
}

// إظهار الإشعارات
function showNotifications() {
    const panel = document.getElementById('notificationPanel');
    panel.classList.add('show');
}

// إخفاء الإشعارات
function hideNotifications() {
    const panel = document.getElementById('notificationPanel');
    panel.classList.remove('show');
}

// تنسيق العملة
function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-SA', {
        style: 'decimal',
        minimumFractionDigits: 0,
        maximumFractionDigits: 2
    }).format(amount || 0);
}

// إظهار رسالة خطأ
function showError(message) {
    // يمكن تحسين هذا لاحقاً بإضافة نظام إشعارات أفضل
    alert(message);
}

// معالج النقر خارج القوائم المنسدلة
document.addEventListener('click', function(e) {
    // إغلاق قائمة المستخدم
    if (!e.target.closest('.user-menu')) {
        const dropdown = document.getElementById('userDropdown');
        dropdown.classList.remove('show');
    }
    
    // إغلاق لوحة الإشعارات
    if (!e.target.closest('.notification-panel') && !e.target.closest('.notification-btn')) {
        hideNotifications();
    }
});

// معالج تغيير حجم النافذة
window.addEventListener('resize', function() {
    if (window.innerWidth > 1024) {
        const sidebar = document.getElementById('sidebar');
        sidebar.classList.remove('show');
    }
});

// دالة إظهار نافذة إضافة جهاز - سيتم استدعاؤها من devices.js
function showAddDeviceModal() {
    // التحقق من وجود الدالة في devices.js
    if (typeof window.showAddDeviceModal !== 'undefined') {
        return; // تجنب التداخل
    }

    // إذا لم يتم تحميل devices.js بعد
    showSection('devices');
    setTimeout(() => {
        if (typeof initializeDevices === 'function') {
            initializeDevices();
        }
        // محاولة استدعاء الدالة مرة أخرى
        const deviceModal = document.getElementById('deviceModal');
        if (deviceModal) {
            deviceModal.classList.add('show');
        }
    }, 100);
}

// دالة إظهار نافذة إضافة عميل - سيتم استدعاؤها من customers.js
function showAddCustomerModal() {
    // التحقق من وجود الدالة في customers.js
    if (typeof window.showAddCustomerModal !== 'undefined') {
        return; // تجنب التداخل
    }

    // إذا لم يتم تحميل customers.js بعد
    showSection('customers');
    setTimeout(() => {
        if (typeof initializeCustomers === 'function') {
            initializeCustomers();
        }
        // محاولة استدعاء الدالة مرة أخرى
        const customerModal = document.getElementById('customerModal');
        if (customerModal) {
            customerModal.classList.add('show');
        }
    }, 100);
}

function showAddSparePartModal() {
    alert('نافذة إضافة قطعة غيار قيد التطوير');
}

// دالة إظهار نافذة إضافة مستخدم - سيتم استدعاؤها من users.js
function showAddUserModal() {
    // التحقق من وجود الدالة في users.js
    if (typeof window.showAddUserModal !== 'undefined') {
        return; // تجنب التداخل
    }

    // إذا لم يتم تحميل users.js بعد
    showSection('users');
    setTimeout(() => {
        if (typeof initializeUsers === 'function') {
            initializeUsers();
        }
        // محاولة استدعاء الدالة مرة أخرى
        const userModal = document.getElementById('userModal');
        if (userModal) {
            userModal.classList.add('show');
        }
    }, 100);
}

function generateReport() {
    alert('إنشاء التقارير قيد التطوير');
}

function showProfile() {
    alert('الملف الشخصي قيد التطوير');
}

function showSettings() {
    alert('الإعدادات قيد التطوير');
}
