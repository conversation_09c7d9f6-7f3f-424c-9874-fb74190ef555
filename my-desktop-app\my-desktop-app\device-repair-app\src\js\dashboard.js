const { ipc<PERSON>enderer } = require('electron');

// متغيرات عامة
let currentUser = null;
let dashboardData = {};

// دالة للحصول على المستخدم الحالي
function getCurrentUser() {
    try {
        const userString = sessionStorage.getItem('currentUser');
        if (userString) {
            return JSON.parse(userString);
        }
        return null;
    } catch (error) {
        console.error('خطأ في قراءة بيانات المستخدم:', error);
        return null;
    }
}

// تهيئة لوحة التحكم
document.addEventListener('DOMContentLoaded', function() {
    // معالج الأخطاء العامة
    window.addEventListener('error', function(e) {
        console.error('خطأ عام في التطبيق:', e.error);
    });

    window.addEventListener('unhandledrejection', function(e) {
        console.error('خطأ في Promise غير معالج:', e.reason);
        e.preventDefault();
    });

    // تهيئة لوحة التحكم
    setTimeout(() => {
        initializeDashboard();
    }, 100);
});

// تهيئة لوحة التحكم
async function initializeDashboard() {
    try {
        console.log('بدء تهيئة لوحة التحكم...');

        // التحقق من تسجيل الدخول
        currentUser = getCurrentUser();
        if (!currentUser) {
            console.log('لم يتم العثور على مستخدم مسجل، إعادة توجيه لصفحة تسجيل الدخول');
            window.location.href = 'login.html';
            return;
        }

        console.log('تم العثور على المستخدم:', currentUser.username);

        // تحديث معلومات المستخدم في الواجهة
        updateUserInfo();

        // تحديد الصلاحيات
        setUserPermissions();

        // تحميل بيانات لوحة التحكم
        console.log('تحميل بيانات لوحة التحكم...');
        await loadDashboardData();

        // تحديث الإحصائيات
        console.log('تحديث الإحصائيات...');
        updateStatistics();

        // تحميل الأجهزة الحديثة
        console.log('تحميل الأجهزة الحديثة...');
        await loadRecentDevices();

        // تحميل قطع الغيار المنخفضة
        console.log('تحميل قطع الغيار المنخفضة...');
        await loadLowStockItems();

        // تحديث الإشعارات
        console.log('تحديث الإشعارات...');
        await updateNotifications();

        console.log('تم تهيئة لوحة التحكم بنجاح');

    } catch (error) {
        console.error('خطأ في تهيئة لوحة التحكم:', error);
        showError('حدث خطأ في تحميل لوحة التحكم: ' + error.message);
    }
}

// تحديث معلومات المستخدم
function updateUserInfo() {
    try {
        if (currentUser) {
            const userNameElement = document.getElementById('userName');
            const userRoleElement = document.getElementById('userRole');

            if (userNameElement) {
                userNameElement.textContent = currentUser.full_name || currentUser.username || 'مستخدم';
            }

            if (userRoleElement) {
                userRoleElement.textContent = getRoleDisplayName(currentUser.role);
            }
        }
    } catch (error) {
        console.error('خطأ في تحديث معلومات المستخدم:', error);
    }
}

// الحصول على اسم الدور للعرض
function getRoleDisplayName(role) {
    const roles = {
        'admin': 'مدير',
        'engineer': 'مهندس',
        'receptionist': 'موظف استقبال'
    };
    return roles[role] || role;
}

// تحديد الصلاحيات
function setUserPermissions() {
    try {
        if (currentUser && currentUser.role === 'admin') {
            document.body.classList.add('admin');
        } else {
            document.body.classList.remove('admin');
        }

        // إخفاء/إظهار العناصر حسب الدور
        const adminElements = document.querySelectorAll('.admin-only');
        adminElements.forEach(element => {
            if (currentUser && currentUser.role === 'admin') {
                element.style.display = '';
            } else {
                element.style.display = 'none';
            }
        });
    } catch (error) {
        console.error('خطأ في تحديد الصلاحيات:', error);
    }
}

// تحميل بيانات لوحة التحكم
async function loadDashboardData() {
    try {
        // التحقق من وجود ipcRenderer
        if (!ipcRenderer) {
            console.warn('ipcRenderer غير متاح، استخدام بيانات افتراضية');
            dashboardData = {
                totalDevices: 0,
                pendingDevices: 0,
                completedDevices: 0,
                monthlyRevenue: 0
            };
            return;
        }

        // إحصائيات الأجهزة
        const totalDevices = await ipcRenderer.invoke('database-get',
            'SELECT COUNT(*) as count FROM devices'
        ).catch(() => ({ count: 0 }));

        const pendingDevices = await ipcRenderer.invoke('database-get',
            'SELECT COUNT(*) as count FROM devices WHERE status IN ("received", "inspecting", "repairing")'
        ).catch(() => ({ count: 0 }));

        const completedDevices = await ipcRenderer.invoke('database-get',
            'SELECT COUNT(*) as count FROM devices WHERE status = "completed"'
        ).catch(() => ({ count: 0 }));

        // إيرادات الشهر الحالي
        const currentMonth = new Date().toISOString().slice(0, 7);
        const monthlyRevenue = await ipcRenderer.invoke('database-get',
            'SELECT SUM(final_cost) as total FROM devices WHERE DATE(created_at) LIKE ? AND status = "delivered"',
            [`${currentMonth}%`]
        ).catch(() => ({ total: 0 }));

        dashboardData = {
            totalDevices: totalDevices?.count || 0,
            pendingDevices: pendingDevices?.count || 0,
            completedDevices: completedDevices?.count || 0,
            monthlyRevenue: monthlyRevenue?.total || 0
        };

    } catch (error) {
        console.error('خطأ في تحميل بيانات لوحة التحكم:', error);
        // استخدام بيانات افتراضية في حالة الخطأ
        dashboardData = {
            totalDevices: 0,
            pendingDevices: 0,
            completedDevices: 0,
            monthlyRevenue: 0
        };
    }
}

// تحديث الإحصائيات
function updateStatistics() {
    try {
        const totalDevicesElement = document.getElementById('totalDevices');
        const pendingDevicesElement = document.getElementById('pendingDevices');
        const completedDevicesElement = document.getElementById('completedDevices');
        const monthlyRevenueElement = document.getElementById('monthlyRevenue');

        if (totalDevicesElement) {
            totalDevicesElement.textContent = dashboardData.totalDevices || 0;
        }

        if (pendingDevicesElement) {
            pendingDevicesElement.textContent = dashboardData.pendingDevices || 0;
        }

        if (completedDevicesElement) {
            completedDevicesElement.textContent = dashboardData.completedDevices || 0;
        }

        if (monthlyRevenueElement) {
            monthlyRevenueElement.textContent = formatCurrency(dashboardData.monthlyRevenue || 0);
        }
    } catch (error) {
        console.error('خطأ في تحديث الإحصائيات:', error);
    }
}

// تحميل الأجهزة الحديثة
async function loadRecentDevices() {
    try {
        const container = document.getElementById('recentDevices');
        if (!container) return;

        if (!ipcRenderer) {
            container.innerHTML = '<p class="text-muted">لا توجد أجهزة حديثة</p>';
            return;
        }

        const recentDevices = await ipcRenderer.invoke('database-all',
            `SELECT d.*, c.name as customer_name
             FROM devices d
             LEFT JOIN customers c ON d.customer_id = c.id
             ORDER BY d.created_at DESC
             LIMIT 5`
        ).catch(() => []);

        container.innerHTML = '';

        if (recentDevices && recentDevices.length > 0) {
            recentDevices.forEach(device => {
                const deviceElement = createDeviceElement(device);
                container.appendChild(deviceElement);
            });
        } else {
            container.innerHTML = '<p class="text-muted">لا توجد أجهزة حديثة</p>';
        }

    } catch (error) {
        console.error('خطأ في تحميل الأجهزة الحديثة:', error);
        const container = document.getElementById('recentDevices');
        if (container) {
            container.innerHTML = '<p class="text-muted">خطأ في تحميل الأجهزة</p>';
        }
    }
}

// إنشاء عنصر جهاز
function createDeviceElement(device) {
    const div = document.createElement('div');
    div.className = 'device-item';
    
    div.innerHTML = `
        <div class="device-info">
            <div class="device-name">${device.brand} ${device.model}</div>
            <div class="device-status">العميل: ${device.customer_name || 'غير محدد'}</div>
        </div>
        <div class="status-badge status-${device.status}">
            ${getStatusDisplayName(device.status)}
        </div>
    `;
    
    return div;
}

// الحصول على اسم الحالة للعرض
function getStatusDisplayName(status) {
    const statuses = {
        'received': 'مستلم',
        'inspecting': 'قيد الفحص',
        'repairing': 'قيد الإصلاح',
        'completed': 'مكتمل',
        'delivered': 'مُسلم',
        'cancelled': 'ملغي'
    };
    return statuses[status] || status;
}

// تحميل قطع الغيار المنخفضة
async function loadLowStockItems() {
    try {
        const container = document.getElementById('lowStockItems');
        if (!container) return;

        if (!ipcRenderer) {
            container.innerHTML = '<p class="text-muted">جميع قطع الغيار متوفرة</p>';
            return;
        }

        const lowStockItems = await ipcRenderer.invoke('database-all',
            'SELECT * FROM spare_parts WHERE quantity_in_stock <= minimum_stock_level ORDER BY quantity_in_stock ASC LIMIT 5'
        ).catch(() => []);

        container.innerHTML = '';

        if (lowStockItems && lowStockItems.length > 0) {
            lowStockItems.forEach(item => {
                const itemElement = createStockItemElement(item);
                container.appendChild(itemElement);
            });
        } else {
            container.innerHTML = '<p class="text-muted">جميع قطع الغيار متوفرة</p>';
        }

    } catch (error) {
        console.error('خطأ في تحميل قطع الغيار المنخفضة:', error);
        const container = document.getElementById('lowStockItems');
        if (container) {
            container.innerHTML = '<p class="text-muted">خطأ في تحميل قطع الغيار</p>';
        }
    }
}

// إنشاء عنصر قطعة غيار
function createStockItemElement(item) {
    const div = document.createElement('div');
    div.className = 'stock-item';
    
    const stockClass = item.quantity_in_stock === 0 ? 'stock-low' : 'stock-warning';
    
    div.innerHTML = `
        <div class="stock-info">
            <div class="stock-name">${item.name}</div>
            <div class="stock-quantity">الفئة: ${item.category}</div>
        </div>
        <div class="${stockClass}">
            ${item.quantity_in_stock} قطعة
        </div>
    `;
    
    return div;
}

// تحديث الإشعارات
async function updateNotifications() {
    try {
        const badge = document.getElementById('notificationCount');
        if (!badge) return;

        if (!ipcRenderer) {
            badge.style.display = 'none';
            return;
        }

        // حساب عدد الإشعارات (قطع الغيار المنخفضة + الأجهزة المتأخرة)
        const lowStockCount = await ipcRenderer.invoke('database-get',
            'SELECT COUNT(*) as count FROM spare_parts WHERE quantity_in_stock <= minimum_stock_level'
        ).catch(() => ({ count: 0 }));

        const overdueDevices = await ipcRenderer.invoke('database-get',
            'SELECT COUNT(*) as count FROM devices WHERE expected_delivery_date < DATE("now") AND status NOT IN ("delivered", "cancelled")'
        ).catch(() => ({ count: 0 }));

        const totalNotifications = (lowStockCount?.count || 0) + (overdueDevices?.count || 0);

        if (totalNotifications > 0) {
            badge.textContent = totalNotifications;
            badge.style.display = 'block';
        } else {
            badge.style.display = 'none';
        }

    } catch (error) {
        console.error('خطأ في تحديث الإشعارات:', error);
        const badge = document.getElementById('notificationCount');
        if (badge) {
            badge.style.display = 'none';
        }
    }
}

// تبديل القائمة الجانبية
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    sidebar.classList.toggle('collapsed');
}

// إظهار قسم معين
function showSection(sectionName) {
    // إخفاء جميع الأقسام
    const sections = document.querySelectorAll('.content-section');
    sections.forEach(section => {
        section.classList.remove('active');
    });

    // إظهار القسم المطلوب
    const targetSection = document.getElementById(`${sectionName}-section`);
    if (targetSection) {
        targetSection.classList.add('active');
    }

    // تحديث القائمة الجانبية
    const navItems = document.querySelectorAll('.nav-item');
    navItems.forEach(item => {
        item.classList.remove('active');
    });

    const activeNavItem = document.querySelector(`[onclick="showSection('${sectionName}')"]`).closest('.nav-item');
    if (activeNavItem) {
        activeNavItem.classList.add('active');
    }

    // تهيئة القسم المحدد
    if (sectionName === 'customers') {
        if (typeof initializeCustomers === 'function') {
            initializeCustomers();
        }
    } else if (sectionName === 'devices') {
        if (typeof initializeDevices === 'function') {
            initializeDevices();
        }
    } else if (sectionName === 'users') {
        if (typeof initializeUsers === 'function') {
            initializeUsers();
        }
    } else if (sectionName === 'spare-parts') {
        if (typeof initializeSpareParts === 'function') {
            initializeSpareParts();
        }
    } else if (sectionName === 'reports') {
        if (typeof initializeReports === 'function') {
            initializeReports();
        }
    } else if (sectionName === 'settings') {
        if (typeof initializeSettings === 'function') {
            initializeSettings();
        }
    }

    // إخفاء القائمة الجانبية في الشاشات الصغيرة
    if (window.innerWidth <= 1024) {
        const sidebar = document.getElementById('sidebar');
        sidebar.classList.remove('show');
    }
}

// تبديل قائمة المستخدم
function toggleUserMenu() {
    const dropdown = document.getElementById('userDropdown');
    dropdown.classList.toggle('show');
}

// إظهار الإشعارات
function showNotifications() {
    const panel = document.getElementById('notificationPanel');
    panel.classList.add('show');
}

// إخفاء الإشعارات
function hideNotifications() {
    const panel = document.getElementById('notificationPanel');
    panel.classList.remove('show');
}

// تنسيق العملة
function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-SA', {
        style: 'decimal',
        minimumFractionDigits: 0,
        maximumFractionDigits: 2
    }).format(amount || 0);
}

// إظهار رسالة خطأ
function showError(message) {
    console.error(message);
    // عرض رسالة خطأ في الواجهة
    const errorDiv = document.createElement('div');
    errorDiv.className = 'error-message';
    errorDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #fee2e2;
        color: #dc2626;
        padding: 15px 20px;
        border-radius: 8px;
        border: 1px solid #fecaca;
        z-index: 9999;
        max-width: 400px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    `;
    errorDiv.textContent = message;

    document.body.appendChild(errorDiv);

    // إزالة الرسالة بعد 5 ثوان
    setTimeout(() => {
        if (errorDiv.parentNode) {
            errorDiv.parentNode.removeChild(errorDiv);
        }
    }, 5000);
}

// معالج النقر خارج القوائم المنسدلة والنوافذ المنبثقة
document.addEventListener('click', function(e) {
    // إغلاق قائمة المستخدم
    if (!e.target.closest('.user-menu')) {
        const dropdown = document.getElementById('userDropdown');
        if (dropdown) {
            dropdown.classList.remove('show');
        }
    }

    // إغلاق لوحة الإشعارات
    if (!e.target.closest('.notification-panel') && !e.target.closest('.notification-btn')) {
        hideNotifications();
    }

    // إغلاق النوافذ المنبثقة عند النقر على الخلفية
    if (e.target.classList.contains('modal')) {
        e.target.classList.remove('show');
    }
});

// معالج تغيير حجم النافذة
window.addEventListener('resize', function() {
    if (window.innerWidth > 1024) {
        const sidebar = document.getElementById('sidebar');
        if (sidebar) {
            sidebar.classList.remove('show');
        }
    }
});

// معالج مفتاح Escape لإغلاق النوافذ المنبثقة
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        // إغلاق جميع النوافذ المنبثقة المفتوحة
        const modals = document.querySelectorAll('.modal.show');
        modals.forEach(modal => {
            modal.classList.remove('show');
        });

        // إغلاق القوائم المنسدلة
        const dropdown = document.getElementById('userDropdown');
        if (dropdown) {
            dropdown.classList.remove('show');
        }

        hideNotifications();
    }
});

// دالة إظهار نافذة إضافة جهاز
function showAddDeviceModal() {
    // التأكد من تحميل القسم أولاً
    showSection('devices');

    setTimeout(() => {
        // تهيئة الأجهزة إذا لم يتم تهيئتها
        if (typeof initializeDevices === 'function') {
            initializeDevices();
        }

        // إظهار النافذة
        setTimeout(() => {
            const modal = document.getElementById('deviceModal');
            const form = document.getElementById('deviceForm');
            const title = document.getElementById('deviceModalTitle');

            if (modal && form && title) {
                title.textContent = 'إضافة جهاز جديد';
                form.reset();

                // تعيين التاريخ الحالي
                const today = new Date().toISOString().split('T')[0];
                const receivedDateInput = document.getElementById('receivedDate');
                if (receivedDateInput) {
                    receivedDateInput.value = today;
                }

                modal.classList.add('show');
            }
        }, 200);
    }, 100);
}

// دالة إظهار نافذة إضافة عميل
function showAddCustomerModal() {
    // التأكد من تحميل القسم أولاً
    showSection('customers');

    setTimeout(() => {
        // تهيئة العملاء إذا لم يتم تهيئتها
        if (typeof initializeCustomers === 'function') {
            initializeCustomers();
        }

        // إظهار النافذة
        setTimeout(() => {
            const modal = document.getElementById('customerModal');
            const form = document.getElementById('customerForm');
            const title = document.getElementById('modalTitle');

            if (modal && form && title) {
                title.textContent = 'إضافة عميل جديد';
                form.reset();
                modal.classList.add('show');
            }
        }, 200);
    }, 100);
}

// دالة إظهار نافذة إضافة قطعة غيار
function showAddSparePartModal() {
    // التأكد من تحميل القسم أولاً
    showSection('spare-parts');

    setTimeout(() => {
        // تهيئة قطع الغيار إذا لم يتم تهيئتها
        if (typeof initializeSpareParts === 'function') {
            initializeSpareParts();
        }

        // إظهار النافذة
        setTimeout(() => {
            const modal = document.getElementById('sparePartModal');
            const form = document.getElementById('sparePartForm');
            const title = document.getElementById('sparePartModalTitle');

            if (modal && form && title) {
                title.textContent = 'إضافة قطعة غيار جديدة';
                form.reset();
                modal.classList.add('show');
            }
        }, 200);
    }, 100);
}

// دالة إظهار نافذة إضافة مستخدم
function showAddUserModal() {
    // التأكد من تحميل القسم أولاً
    showSection('users');

    setTimeout(() => {
        // تهيئة المستخدمين إذا لم يتم تهيئتها
        if (typeof initializeUsers === 'function') {
            initializeUsers();
        }

        // إظهار النافذة
        setTimeout(() => {
            const modal = document.getElementById('userModal');
            const form = document.getElementById('userForm');
            const title = document.getElementById('userModalTitle');
            const passwordGroup = document.getElementById('passwordGroup');

            if (modal && form && title) {
                title.textContent = 'إضافة مستخدم جديد';
                form.reset();

                // إظهار حقل كلمة المرور للمستخدم الجديد
                if (passwordGroup) {
                    passwordGroup.style.display = 'block';
                    const passwordInput = document.getElementById('userPassword');
                    if (passwordInput) {
                        passwordInput.required = true;
                    }
                }

                modal.classList.add('show');
            }
        }, 200);
    }, 100);
}

// دالة إنشاء التقارير - سيتم استدعاؤها من reports.js
function generateReport() {
    // التحقق من وجود الدالة في reports.js
    if (typeof window.generateReport !== 'undefined') {
        return; // تجنب التداخل
    }

    // إذا لم يتم تحميل reports.js بعد
    showSection('reports');
    setTimeout(() => {
        if (typeof initializeReports === 'function') {
            initializeReports();
        }
    }, 100);
}

function showProfile() {
    showSection('settings');
}

function showSettings() {
    showSection('settings');
}

// دوال إخفاء النوافذ المنبثقة
function hideCustomerModal() {
    const modal = document.getElementById('customerModal');
    if (modal) {
        modal.classList.remove('show');
    }
}

function hideCustomerDetailsModal() {
    const modal = document.getElementById('customerDetailsModal');
    if (modal) {
        modal.classList.remove('show');
    }
}

function hideDeviceModal() {
    const modal = document.getElementById('deviceModal');
    if (modal) {
        modal.classList.remove('show');
    }
}

function hideSparePartModal() {
    const modal = document.getElementById('sparePartModal');
    if (modal) {
        modal.classList.remove('show');
    }
}

function hideUserModal() {
    const modal = document.getElementById('userModal');
    if (modal) {
        modal.classList.remove('show');
    }
}

function hideUserSessionsModal() {
    const modal = document.getElementById('userSessionsModal');
    if (modal) {
        modal.classList.remove('show');
    }
}

// دالة إضافة البيانات التجريبية
async function addSampleDataWrapper() {
    if (typeof addSampleData === 'function') {
        try {
            await addSampleData();
            alert('تم إضافة البيانات التجريبية بنجاح! يرجى إعادة تحميل الصفحة لرؤية البيانات.');
            location.reload();
        } catch (error) {
            console.error('خطأ في إضافة البيانات التجريبية:', error);
            alert('حدث خطأ في إضافة البيانات التجريبية');
        }
    } else {
        alert('دالة إضافة البيانات التجريبية غير متاحة');
    }
}

// دوال مساعدة
function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-SA', {
        style: 'decimal',
        minimumFractionDigits: 0,
        maximumFractionDigits: 2
    }).format(amount || 0) + ' ريال';
}

function getRoleDisplayName(role) {
    const roles = {
        'admin': 'مدير',
        'engineer': 'مهندس',
        'receptionist': 'موظف استقبال'
    };
    return roles[role] || role || 'مستخدم';
}

function formatDate(dateString) {
    try {
        const date = new Date(dateString);
        return date.toLocaleDateString('ar-SA');
    } catch (error) {
        return 'تاريخ غير صحيح';
    }
}

// إنشاء عنصر جهاز
function createDeviceElement(device) {
    const div = document.createElement('div');
    div.className = 'device-item';
    div.innerHTML = `
        <div class="device-info">
            <h4>${device.brand} ${device.model}</h4>
            <p>العميل: ${device.customer_name || 'غير محدد'}</p>
            <p>الحالة: ${getStatusDisplayName(device.status)}</p>
        </div>
        <div class="device-date">
            ${formatDate(device.created_at)}
        </div>
    `;
    return div;
}

// إنشاء عنصر قطعة غيار
function createStockItemElement(item) {
    const div = document.createElement('div');
    div.className = 'stock-item';
    div.innerHTML = `
        <div class="stock-info">
            <h4>${item.name}</h4>
            <p>الكمية: ${item.quantity_in_stock}</p>
            <p>الحد الأدنى: ${item.minimum_stock_level}</p>
        </div>
        <div class="stock-status ${getStockStatusClass(item)}">
            ${getStockStatusText(item)}
        </div>
    `;
    return div;
}

// الحصول على اسم الحالة للعرض
function getStatusDisplayName(status) {
    const statuses = {
        'received': 'مستلم',
        'inspecting': 'قيد الفحص',
        'repairing': 'قيد الإصلاح',
        'completed': 'مكتمل',
        'delivered': 'مُسلم',
        'cancelled': 'ملغي'
    };
    return statuses[status] || status;
}

// الحصول على فئة CSS لحالة المخزون
function getStockStatusClass(item) {
    if (item.quantity_in_stock === 0) return 'out-of-stock';
    if (item.quantity_in_stock <= item.minimum_stock_level) return 'low-stock';
    return 'in-stock';
}

// الحصول على نص حالة المخزون
function getStockStatusText(item) {
    if (item.quantity_in_stock === 0) return 'نفد';
    if (item.quantity_in_stock <= item.minimum_stock_level) return 'منخفض';
    return 'متوفر';
}

// جعل الدوال متاحة عالمياً
window.hideCustomerModal = hideCustomerModal;
window.hideCustomerDetailsModal = hideCustomerDetailsModal;
window.hideDeviceModal = hideDeviceModal;
window.hideSparePartModal = hideSparePartModal;
window.hideUserModal = hideUserModal;
window.hideUserSessionsModal = hideUserSessionsModal;
window.addSampleData = addSampleDataWrapper;
