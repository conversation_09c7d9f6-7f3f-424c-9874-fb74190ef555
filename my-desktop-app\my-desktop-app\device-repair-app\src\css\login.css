/* صفحة تسجيل الدخول */
.login-body {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: flex-start;
    justify-content: center;
    padding: 20px;
    position: relative;
    overflow-x: hidden;
    overflow-y: auto;
}

.login-body::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="10" cy="50" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="90" cy="30" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

.login-container {
    display: flex;
    max-width: 1200px;
    width: 100%;
    gap: 40px;
    align-items: flex-start;
    margin-top: 20px;
    margin-bottom: 20px;
}

.login-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    flex: 1;
    max-width: 500px;
    animation: slideInRight 0.6s ease-out;
}

.features-panel {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 40px;
    flex: 1;
    max-width: 600px;
    color: white;
    animation: slideInLeft 0.6s ease-out;
}

.login-header {
    text-align: center;
    margin-bottom: 40px;
}

.logo {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 32px;
    color: white;
    box-shadow: 0 10px 20px rgba(79, 70, 229, 0.3);
}

.login-header h1 {
    font-size: 28px;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 10px;
}

.login-header p {
    color: #6b7280;
    font-size: 16px;
    line-height: 1.5;
}

.login-form {
    margin-bottom: 30px;
}

.form-group {
    margin-bottom: 25px;
}

.form-label {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--dark-color);
}

.form-label i {
    color: var(--primary-color);
}

.password-input {
    position: relative;
}

.password-toggle {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: color 0.3s ease;
}

.password-toggle:hover {
    color: var(--primary-color);
}

.checkbox-container {
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    font-weight: 500;
    color: var(--dark-color);
}

.checkbox-container input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-radius: 4px;
    position: relative;
    transition: all 0.3s ease;
}

.checkbox-container input[type="checkbox"]:checked + .checkmark {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.checkbox-container input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.btn-login {
    width: 100%;
    padding: 15px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 10px;
    margin-bottom: 20px;
    transition: all 0.3s ease;
}

.btn-login:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(79, 70, 229, 0.3);
}

.error-message {
    background: #fef2f2;
    color: var(--danger-color);
    padding: 12px 16px;
    border-radius: 8px;
    border: 1px solid #fecaca;
    margin-top: 15px;
    font-weight: 500;
    text-align: center;
}

.loading-message {
    background: #eff6ff;
    color: var(--primary-color);
    padding: 12px 16px;
    border-radius: 8px;
    border: 1px solid #bfdbfe;
    margin-top: 15px;
    font-weight: 500;
    text-align: center;
}

.login-footer {
    border-top: 1px solid var(--border-color);
    padding-top: 30px;
    margin-top: 30px;
}

.system-info, .default-credentials {
    margin-bottom: 25px;
}

.system-info h3, .default-credentials h3 {
    font-size: 16px;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 15px;
}

.system-info ul {
    list-style: none;
    padding: 0;
}

.system-info li {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 0;
    color: #6b7280;
    font-size: 14px;
}

.system-info i {
    color: var(--primary-color);
    width: 16px;
}

.default-credentials p {
    margin: 5px 0;
    font-size: 14px;
    color: #6b7280;
}

.default-credentials code {
    background: #f3f4f6;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    color: var(--primary-color);
    font-weight: 600;
}

/* لوحة المميزات */
.features-panel h2 {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 30px;
    text-align: center;
}

.feature-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    margin-bottom: 25px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    transition: all 0.3s ease;
}

.feature-item:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateX(-5px);
}

.feature-item i {
    font-size: 24px;
    color: #fbbf24;
    margin-top: 5px;
    min-width: 30px;
}

.feature-item h3 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 8px;
    color: white;
}

.feature-item p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    line-height: 1.5;
}

/* الرسوم المتحركة */
@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* التصميم المتجاوب */
@media (max-width: 1024px) {
    .login-container {
        flex-direction: column;
        max-width: 600px;
        gap: 20px;
    }

    .features-panel {
        order: -1;
        max-height: 400px;
        overflow-y: auto;
    }
}

@media (max-width: 768px) {
    .login-body {
        padding: 10px;
        align-items: flex-start;
    }

    .login-container {
        margin-top: 10px;
        margin-bottom: 10px;
    }

    .login-card, .features-panel {
        padding: 20px 15px;
    }

    .login-header h1 {
        font-size: 20px;
    }

    .login-header p {
        font-size: 14px;
    }

    .features-panel {
        max-height: 300px;
    }

    .features-panel h2 {
        font-size: 18px;
    }

    .feature-item {
        padding: 12px;
        margin-bottom: 15px;
    }

    .feature-item h3 {
        font-size: 14px;
    }

    .feature-item p {
        font-size: 12px;
    }

    .logo {
        width: 60px;
        height: 60px;
        font-size: 24px;
    }
}

@media (max-height: 700px) {
    .login-body {
        align-items: flex-start;
        padding-top: 10px;
    }

    .login-container {
        margin-top: 10px;
    }

    .features-panel {
        max-height: 250px;
        overflow-y: auto;
    }

    .login-card {
        padding: 25px;
    }
}
