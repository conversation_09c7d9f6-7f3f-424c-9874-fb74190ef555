const { ipc<PERSON><PERSON><PERSON> } = require('electron');
const bcrypt = require('bcrypt');

// متغيرات عامة
let currentUser = null;

// تهيئة صفحة الإعدادات
function initializeSettings() {
    currentUser = getCurrentUser();
    if (!currentUser) {
        showSection('dashboard');
        return;
    }
    
    loadUserProfile();
    loadSystemInfo();
    setupSettingsEventListeners();
}

// إعداد مستمعي الأحداث
function setupSettingsEventListeners() {
    // نموذج الملف الشخصي
    const profileForm = document.getElementById('profileForm');
    if (profileForm) {
        profileForm.addEventListener('submit', handleProfileUpdate);
    }
    
    // نموذج تغيير كلمة المرور
    const passwordForm = document.getElementById('passwordForm');
    if (passwordForm) {
        passwordForm.addEventListener('submit', handlePasswordChange);
    }
    
    // إعدادات النظام
    const notificationsEnabled = document.getElementById('notificationsEnabled');
    if (notificationsEnabled) {
        notificationsEnabled.addEventListener('change', handleNotificationToggle);
    }
    
    const autoBackup = document.getElementById('autoBackup');
    if (autoBackup) {
        autoBackup.addEventListener('change', handleAutoBackupToggle);
    }
    
    const darkMode = document.getElementById('darkMode');
    if (darkMode) {
        darkMode.addEventListener('change', handleDarkModeToggle);
    }
}

// تحميل الملف الشخصي
function loadUserProfile() {
    if (!currentUser) return;
    
    document.getElementById('profileFullName').value = currentUser.full_name || '';
    document.getElementById('profileUsername').value = currentUser.username || '';
    document.getElementById('profileEmail').value = currentUser.email || '';
    document.getElementById('profilePhone').value = currentUser.phone || '';
}

// تحميل معلومات النظام
async function loadSystemInfo() {
    try {
        // عدد المستخدمين
        const usersCount = await ipcRenderer.invoke('database-get',
            'SELECT COUNT(*) as count FROM users'
        );
        
        // عدد الأجهزة
        const devicesCount = await ipcRenderer.invoke('database-get',
            'SELECT COUNT(*) as count FROM devices'
        );
        
        // عدد العملاء
        const customersCount = await ipcRenderer.invoke('database-get',
            'SELECT COUNT(*) as count FROM customers'
        );
        
        // آخر تحديث
        const lastUpdate = await ipcRenderer.invoke('database-get',
            'SELECT MAX(updated_at) as last_update FROM users'
        );
        
        // تحديث الواجهة
        document.getElementById('totalUsers').textContent = usersCount?.count || 0;
        document.getElementById('totalDevicesInfo').textContent = devicesCount?.count || 0;
        document.getElementById('totalCustomersInfo').textContent = customersCount?.count || 0;
        document.getElementById('lastUpdate').textContent = lastUpdate?.last_update ? 
            formatDate(lastUpdate.last_update) : 'غير محدد';
        
    } catch (error) {
        console.error('خطأ في تحميل معلومات النظام:', error);
    }
}

// معالج تحديث الملف الشخصي
async function handleProfileUpdate(e) {
    e.preventDefault();
    
    const email = document.getElementById('profileEmail').value.trim();
    const phone = document.getElementById('profilePhone').value.trim();
    
    // التحقق من صحة البريد الإلكتروني
    if (email && !isValidEmail(email)) {
        showNotification('البريد الإلكتروني غير صحيح', 'error');
        return;
    }
    
    try {
        await ipcRenderer.invoke('database-run',
            'UPDATE users SET email = ?, phone = ?, updated_at = ? WHERE id = ?',
            [email, phone, new Date().toISOString(), currentUser.id]
        );
        
        // تحديث البيانات المحلية
        currentUser.email = email;
        currentUser.phone = phone;
        sessionStorage.setItem('currentUser', JSON.stringify(currentUser));
        
        showNotification('تم تحديث الملف الشخصي بنجاح', 'success');
        
    } catch (error) {
        console.error('خطأ في تحديث الملف الشخصي:', error);
        showNotification('حدث خطأ في تحديث الملف الشخصي', 'error');
    }
}

// معالج تغيير كلمة المرور
async function handlePasswordChange(e) {
    e.preventDefault();
    
    const currentPassword = document.getElementById('currentPassword').value;
    const newPassword = document.getElementById('newPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;
    
    // التحقق من تطابق كلمة المرور الجديدة
    if (newPassword !== confirmPassword) {
        showNotification('كلمة المرور الجديدة غير متطابقة', 'error');
        return;
    }
    
    // التحقق من طول كلمة المرور
    if (newPassword.length < 6) {
        showNotification('كلمة المرور يجب أن تكون 6 أحرف على الأقل', 'error');
        return;
    }
    
    try {
        // التحقق من كلمة المرور الحالية
        const user = await ipcRenderer.invoke('database-get',
            'SELECT password FROM users WHERE id = ?',
            [currentUser.id]
        );
        
        const passwordMatch = await bcrypt.compare(currentPassword, user.password);
        if (!passwordMatch) {
            showNotification('كلمة المرور الحالية غير صحيحة', 'error');
            return;
        }
        
        // تشفير كلمة المرور الجديدة
        const hashedPassword = await bcrypt.hash(newPassword, 10);
        
        // تحديث كلمة المرور
        await ipcRenderer.invoke('database-run',
            'UPDATE users SET password = ?, updated_at = ? WHERE id = ?',
            [hashedPassword, new Date().toISOString(), currentUser.id]
        );
        
        // مسح النموذج
        document.getElementById('passwordForm').reset();
        
        showNotification('تم تغيير كلمة المرور بنجاح', 'success');
        
    } catch (error) {
        console.error('خطأ في تغيير كلمة المرور:', error);
        showNotification('حدث خطأ في تغيير كلمة المرور', 'error');
    }
}

// معالج تبديل الإشعارات
function handleNotificationToggle(e) {
    const enabled = e.target.checked;
    localStorage.setItem('notificationsEnabled', enabled);
    showNotification(`تم ${enabled ? 'تفعيل' : 'تعطيل'} الإشعارات`, 'success');
}

// معالج تبديل النسخ الاحتياطي التلقائي
function handleAutoBackupToggle(e) {
    const enabled = e.target.checked;
    localStorage.setItem('autoBackup', enabled);
    showNotification(`تم ${enabled ? 'تفعيل' : 'تعطيل'} النسخ الاحتياطي التلقائي`, 'success');
}

// معالج تبديل الوضع المظلم
function handleDarkModeToggle(e) {
    const enabled = e.target.checked;
    localStorage.setItem('darkMode', enabled);
    
    if (enabled) {
        document.body.classList.add('dark-mode');
    } else {
        document.body.classList.remove('dark-mode');
    }
    
    showNotification(`تم ${enabled ? 'تفعيل' : 'تعطيل'} الوضع المظلم`, 'success');
}

// إنشاء نسخة احتياطية
async function createBackup() {
    try {
        showNotification('جاري إنشاء النسخة الاحتياطية...', 'info');
        
        // الحصول على جميع البيانات
        const users = await ipcRenderer.invoke('database-all', 'SELECT * FROM users');
        const customers = await ipcRenderer.invoke('database-all', 'SELECT * FROM customers');
        const devices = await ipcRenderer.invoke('database-all', 'SELECT * FROM devices');
        const spareParts = await ipcRenderer.invoke('database-all', 'SELECT * FROM spare_parts');
        
        const backupData = {
            timestamp: new Date().toISOString(),
            version: '1.0.0',
            data: {
                users,
                customers,
                devices,
                spare_parts: spareParts
            }
        };
        
        // حفظ الملف
        const { dialog } = require('electron').remote || require('@electron/remote');
        const fs = require('fs');
        
        const result = await dialog.showSaveDialog({
            title: 'حفظ النسخة الاحتياطية',
            defaultPath: `backup_${new Date().toISOString().split('T')[0]}.json`,
            filters: [
                { name: 'JSON Files', extensions: ['json'] }
            ]
        });
        
        if (!result.canceled) {
            fs.writeFileSync(result.filePath, JSON.stringify(backupData, null, 2));
            showNotification('تم إنشاء النسخة الاحتياطية بنجاح', 'success');
        }
        
    } catch (error) {
        console.error('خطأ في إنشاء النسخة الاحتياطية:', error);
        showNotification('حدث خطأ في إنشاء النسخة الاحتياطية', 'error');
    }
}

// استعادة البيانات
async function restoreBackup() {
    if (!confirm('هل أنت متأكد من استعادة البيانات؟ سيتم استبدال جميع البيانات الحالية.')) {
        return;
    }
    
    try {
        const { dialog } = require('electron').remote || require('@electron/remote');
        const fs = require('fs');
        
        const result = await dialog.showOpenDialog({
            title: 'اختر ملف النسخة الاحتياطية',
            filters: [
                { name: 'JSON Files', extensions: ['json'] }
            ]
        });
        
        if (!result.canceled && result.filePaths.length > 0) {
            const backupData = JSON.parse(fs.readFileSync(result.filePaths[0], 'utf8'));
            
            // التحقق من صحة الملف
            if (!backupData.data) {
                showNotification('ملف النسخة الاحتياطية غير صحيح', 'error');
                return;
            }
            
            showNotification('جاري استعادة البيانات...', 'info');
            
            // مسح البيانات الحالية (عدا المستخدم الحالي)
            await ipcRenderer.invoke('database-run', 'DELETE FROM devices');
            await ipcRenderer.invoke('database-run', 'DELETE FROM customers');
            await ipcRenderer.invoke('database-run', 'DELETE FROM spare_parts');
            
            // استعادة البيانات
            if (backupData.data.customers) {
                for (const customer of backupData.data.customers) {
                    await ipcRenderer.invoke('database-run',
                        'INSERT INTO customers (name, phone, email, address, notes, created_at) VALUES (?, ?, ?, ?, ?, ?)',
                        [customer.name, customer.phone, customer.email, customer.address, customer.notes, customer.created_at]
                    );
                }
            }
            
            // يمكن إضافة المزيد من عمليات الاستعادة هنا
            
            showNotification('تم استعادة البيانات بنجاح', 'success');
        }
        
    } catch (error) {
        console.error('خطأ في استعادة البيانات:', error);
        showNotification('حدث خطأ في استعادة البيانات', 'error');
    }
}

// تصدير البيانات
function exportData() {
    showNotification('تصدير البيانات إلى Excel قيد التطوير', 'info');
}

// التحقق من صحة البريد الإلكتروني
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// تنسيق التاريخ
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA');
}

// إظهار الإشعارات
function showNotification(message, type = 'info') {
    if (type === 'error') {
        alert('خطأ: ' + message);
    } else if (type === 'success') {
        alert('نجح: ' + message);
    } else {
        alert(message);
    }
}

// تحميل الإعدادات المحفوظة
function loadSavedSettings() {
    // تحميل إعدادات الإشعارات
    const notificationsEnabled = localStorage.getItem('notificationsEnabled');
    if (notificationsEnabled !== null) {
        document.getElementById('notificationsEnabled').checked = notificationsEnabled === 'true';
    }
    
    // تحميل إعدادات النسخ الاحتياطي
    const autoBackup = localStorage.getItem('autoBackup');
    if (autoBackup !== null) {
        document.getElementById('autoBackup').checked = autoBackup === 'true';
    }
    
    // تحميل إعدادات الوضع المظلم
    const darkMode = localStorage.getItem('darkMode');
    if (darkMode === 'true') {
        document.getElementById('darkMode').checked = true;
        document.body.classList.add('dark-mode');
    }
}

// تهيئة الإعدادات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadSavedSettings();
});

// تصدير الدوال
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        initializeSettings,
        createBackup,
        restoreBackup,
        exportData
    };
}
