<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - نظام إدارة محلات الصيانة</title>
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../css/dashboard.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="dashboard-body">
    <!-- الشريط العلوي -->
    <header class="top-header">
        <div class="header-left">
            <button class="menu-toggle" onclick="toggleSidebar()">
                <i class="fas fa-bars"></i>
            </button>
            <div class="logo-section">
                <i class="fas fa-tools"></i>
                <span>نظام إدارة محلات الصيانة</span>
            </div>
        </div>
        
        <div class="header-right">
            <div class="notifications">
                <button class="notification-btn" onclick="showNotifications()">
                    <i class="fas fa-bell"></i>
                    <span class="notification-badge" id="notificationCount">3</span>
                </button>
            </div>
            
            <div class="user-menu">
                <button class="user-btn" onclick="toggleUserMenu()">
                    <div class="user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="user-info">
                        <span class="user-name" id="userName">مدير النظام</span>
                        <span class="user-role" id="userRole">مدير</span>
                    </div>
                    <i class="fas fa-chevron-down"></i>
                </button>
                
                <div class="user-dropdown" id="userDropdown">
                    <a href="#" onclick="showProfile()">
                        <i class="fas fa-user-circle"></i>
                        الملف الشخصي
                    </a>
                    <a href="#" onclick="showSettings()">
                        <i class="fas fa-cog"></i>
                        الإعدادات
                    </a>
                    <div class="dropdown-divider"></div>
                    <a href="#" onclick="logout()" class="text-danger">
                        <i class="fas fa-sign-out-alt"></i>
                        تسجيل الخروج
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- الحاوي الرئيسي -->
    <div class="main-container">
        <!-- القائمة الجانبية -->
        <aside class="sidebar" id="sidebar">
            <nav class="sidebar-nav">
                <ul class="nav-list">
                    <li class="nav-item active">
                        <a href="#" class="nav-link" onclick="showSection('dashboard')">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>لوحة التحكم</span>
                        </a>
                    </li>
                    
                    <li class="nav-item">
                        <a href="#" class="nav-link" onclick="showSection('devices')">
                            <i class="fas fa-mobile-alt"></i>
                            <span>إدارة الأجهزة</span>
                            <span class="nav-badge">12</span>
                        </a>
                    </li>
                    
                    <li class="nav-item">
                        <a href="#" class="nav-link" onclick="showSection('customers')">
                            <i class="fas fa-users"></i>
                            <span>إدارة العملاء</span>
                        </a>
                    </li>
                    
                    <li class="nav-item">
                        <a href="#" class="nav-link" onclick="showSection('spare-parts')">
                            <i class="fas fa-cogs"></i>
                            <span>قطع الغيار</span>
                            <span class="nav-badge warning">5</span>
                        </a>
                    </li>
                    
                    <li class="nav-item">
                        <a href="#" class="nav-link" onclick="showSection('reports')">
                            <i class="fas fa-chart-bar"></i>
                            <span>التقارير</span>
                        </a>
                    </li>
                    
                    <li class="nav-item admin-only">
                        <a href="#" class="nav-link" onclick="showSection('users')">
                            <i class="fas fa-user-shield"></i>
                            <span>إدارة المستخدمين</span>
                        </a>
                    </li>
                    
                    <li class="nav-item">
                        <a href="#" class="nav-link" onclick="showSection('settings')">
                            <i class="fas fa-cog"></i>
                            <span>الإعدادات</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- المحتوى الرئيسي -->
        <main class="main-content">
            <!-- قسم لوحة التحكم -->
            <section id="dashboard-section" class="content-section active">
                <div class="section-header">
                    <h1>لوحة التحكم</h1>
                    <div class="header-actions">
                        <p>نظرة عامة على أداء المحل</p>
                        <button class="btn btn-outline" onclick="addSampleData()" title="إضافة بيانات تجريبية للاختبار">
                            <i class="fas fa-database"></i>
                            بيانات تجريبية
                        </button>
                    </div>
                </div>
                
                <!-- البطاقات الإحصائية -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon bg-primary">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="totalDevices">24</h3>
                            <p>إجمالي الأجهزة</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon bg-warning">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="pendingDevices">8</h3>
                            <p>قيد الإصلاح</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon bg-success">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="completedDevices">16</h3>
                            <p>تم الإصلاح</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon bg-secondary">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="monthlyRevenue">15,750</h3>
                            <p>إيرادات الشهر (ريال)</p>
                        </div>
                    </div>
                </div>
                
                <!-- الرسوم البيانية والجداول -->
                <div class="dashboard-grid">
                    <div class="dashboard-card">
                        <div class="card-header">
                            <h3>الأجهزة الحديثة</h3>
                            <button class="btn btn-outline" onclick="showSection('devices')">
                                عرض الكل
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="recent-devices" id="recentDevices">
                                <!-- سيتم تحميل البيانات ديناميكياً -->
                            </div>
                        </div>
                    </div>
                    
                    <div class="dashboard-card">
                        <div class="card-header">
                            <h3>قطع الغيار المنخفضة</h3>
                            <button class="btn btn-outline" onclick="showSection('spare-parts')">
                                عرض الكل
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="low-stock-items" id="lowStockItems">
                                <!-- سيتم تحميل البيانات ديناميكياً -->
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            
            <!-- قسم إدارة الأجهزة -->
            <section id="devices-section" class="content-section">
                <div class="section-header">
                    <h1>إدارة الأجهزة</h1>
                    <div class="header-actions">
                        <div class="search-box">
                            <i class="fas fa-search"></i>
                            <input type="text" id="deviceSearch" placeholder="البحث في الأجهزة...">
                        </div>
                        <select id="statusFilter" class="form-control" style="width: 150px;">
                            <option value="">جميع الحالات</option>
                            <option value="received">مستلم</option>
                            <option value="inspecting">قيد الفحص</option>
                            <option value="repairing">قيد الإصلاح</option>
                            <option value="completed">مكتمل</option>
                            <option value="delivered">مُسلم</option>
                            <option value="cancelled">ملغي</option>
                        </select>
                        <button class="btn btn-primary" onclick="showAddDeviceModal()">
                            <i class="fas fa-plus"></i>
                            إضافة جهاز جديد
                        </button>
                    </div>
                </div>

                <div class="devices-stats">
                    <div class="stat-item">
                        <i class="fas fa-mobile-alt"></i>
                        <div>
                            <span class="stat-number" id="totalDevicesCount">0</span>
                            <span class="stat-label">إجمالي الأجهزة</span>
                        </div>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-clock"></i>
                        <div>
                            <span class="stat-number" id="pendingDevicesCount">0</span>
                            <span class="stat-label">قيد الإصلاح</span>
                        </div>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-check-circle"></i>
                        <div>
                            <span class="stat-number" id="completedDevicesCount">0</span>
                            <span class="stat-label">مكتمل</span>
                        </div>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-handshake"></i>
                        <div>
                            <span class="stat-number" id="deliveredDevicesCount">0</span>
                            <span class="stat-label">مُسلم</span>
                        </div>
                    </div>
                </div>

                <div class="section-content">
                    <div id="devicesContainer" class="devices-grid">
                        <!-- سيتم تحميل الأجهزة هنا -->
                    </div>
                </div>
            </section>
            
            <section id="customers-section" class="content-section">
                <div class="section-header">
                    <h1>إدارة العملاء</h1>
                    <div class="header-actions">
                        <div class="search-box">
                            <i class="fas fa-search"></i>
                            <input type="text" id="customerSearch" placeholder="البحث في العملاء...">
                        </div>
                        <button class="btn btn-primary" onclick="showAddCustomerModal()">
                            <i class="fas fa-plus"></i>
                            إضافة عميل جديد
                        </button>
                    </div>
                </div>

                <div class="customers-stats">
                    <div class="stat-item">
                        <i class="fas fa-users"></i>
                        <div>
                            <span class="stat-number" id="customersCount">0</span>
                            <span class="stat-label">إجمالي العملاء</span>
                        </div>
                    </div>
                </div>

                <div class="section-content">
                    <div id="customersContainer" class="customers-grid">
                        <!-- سيتم تحميل العملاء هنا -->
                    </div>
                </div>
            </section>
            
            <section id="spare-parts-section" class="content-section">
                <div class="section-header">
                    <h1>إدارة قطع الغيار</h1>
                    <div class="header-actions">
                        <div class="search-box">
                            <i class="fas fa-search"></i>
                            <input type="text" id="sparePartSearch" placeholder="البحث في قطع الغيار...">
                        </div>
                        <select id="categoryFilter" class="form-control" style="width: 150px;">
                            <option value="">جميع الفئات</option>
                            <option value="شاشات">شاشات</option>
                            <option value="بطاريات">بطاريات</option>
                            <option value="كابلات">كابلات</option>
                            <option value="شواحن">شواحن</option>
                            <option value="سماعات">سماعات</option>
                            <option value="كاميرات">كاميرات</option>
                            <option value="أزرار">أزرار</option>
                            <option value="مكبرات صوت">مكبرات صوت</option>
                            <option value="أخرى">أخرى</option>
                        </select>
                        <select id="stockFilter" class="form-control" style="width: 150px;">
                            <option value="">جميع المخزون</option>
                            <option value="good">متوفر</option>
                            <option value="low">مخزون منخفض</option>
                            <option value="out">نفد المخزون</option>
                        </select>
                        <button class="btn btn-primary" onclick="showAddSparePartModal()">
                            <i class="fas fa-plus"></i>
                            إضافة قطعة غيار
                        </button>
                    </div>
                </div>

                <div class="spare-parts-stats">
                    <div class="stat-item">
                        <i class="fas fa-cogs"></i>
                        <div>
                            <span class="stat-number" id="totalPartsCount">0</span>
                            <span class="stat-label">إجمالي القطع</span>
                        </div>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-exclamation-triangle text-warning"></i>
                        <div>
                            <span class="stat-number" id="lowStockCount">0</span>
                            <span class="stat-label">مخزون منخفض</span>
                        </div>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-times-circle text-danger"></i>
                        <div>
                            <span class="stat-number" id="outOfStockCount">0</span>
                            <span class="stat-label">نفد المخزون</span>
                        </div>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-dollar-sign text-success"></i>
                        <div>
                            <span class="stat-number" id="totalStockValue">0</span>
                            <span class="stat-label">قيمة المخزون</span>
                        </div>
                    </div>
                </div>

                <div class="section-content">
                    <div id="sparePartsContainer" class="spare-parts-grid">
                        <!-- سيتم تحميل قطع الغيار هنا -->
                    </div>
                </div>
            </section>
            
            <section id="reports-section" class="content-section">
                <div class="section-header">
                    <h1>التقارير والإحصائيات</h1>
                    <div class="header-actions">
                        <select id="reportType" class="form-control" style="width: 200px;">
                            <option value="daily">تقرير يومي</option>
                            <option value="weekly">تقرير أسبوعي</option>
                            <option value="monthly">تقرير شهري</option>
                            <option value="yearly">تقرير سنوي</option>
                            <option value="custom">فترة مخصصة</option>
                        </select>
                        <button class="btn btn-primary" onclick="generateReport()">
                            <i class="fas fa-file-pdf"></i>
                            إنشاء تقرير
                        </button>
                    </div>
                </div>

                <div class="reports-overview">
                    <div class="report-card">
                        <div class="report-icon bg-primary">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="report-content">
                            <h3>تقرير الإيرادات</h3>
                            <p>إجمالي الإيرادات والأرباح</p>
                            <div class="report-stats">
                                <span class="stat-value" id="totalRevenue">0 ريال</span>
                                <span class="stat-label">هذا الشهر</span>
                            </div>
                        </div>
                        <button class="btn btn-outline" onclick="showRevenueReport()">عرض</button>
                    </div>

                    <div class="report-card">
                        <div class="report-icon bg-success">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <div class="report-content">
                            <h3>تقرير الأجهزة</h3>
                            <p>إحصائيات الأجهزة المستلمة والمُسلمة</p>
                            <div class="report-stats">
                                <span class="stat-value" id="devicesThisMonth">0</span>
                                <span class="stat-label">جهاز هذا الشهر</span>
                            </div>
                        </div>
                        <button class="btn btn-outline" onclick="showDevicesReport()">عرض</button>
                    </div>

                    <div class="report-card">
                        <div class="report-icon bg-warning">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="report-content">
                            <h3>تقرير العملاء</h3>
                            <p>إحصائيات العملاء الجدد والنشطين</p>
                            <div class="report-stats">
                                <span class="stat-value" id="newCustomers">0</span>
                                <span class="stat-label">عميل جديد</span>
                            </div>
                        </div>
                        <button class="btn btn-outline" onclick="showCustomersReport()">عرض</button>
                    </div>

                    <div class="report-card">
                        <div class="report-icon bg-secondary">
                            <i class="fas fa-cogs"></i>
                        </div>
                        <div class="report-content">
                            <h3>تقرير المخزون</h3>
                            <p>حالة المخزون وقطع الغيار</p>
                            <div class="report-stats">
                                <span class="stat-value" id="stockValue">0 ريال</span>
                                <span class="stat-label">قيمة المخزون</span>
                            </div>
                        </div>
                        <button class="btn btn-outline" onclick="showStockReport()">عرض</button>
                    </div>
                </div>

                <div class="charts-section">
                    <div class="chart-card">
                        <div class="chart-header">
                            <h3>الإيرادات الشهرية</h3>
                            <select id="revenueChartPeriod" class="form-control" style="width: 150px;">
                                <option value="6months">آخر 6 أشهر</option>
                                <option value="year">هذا العام</option>
                                <option value="2years">آخر سنتين</option>
                            </select>
                        </div>
                        <div class="chart-container">
                            <canvas id="revenueChart" width="400" height="200"></canvas>
                        </div>
                    </div>

                    <div class="chart-card">
                        <div class="chart-header">
                            <h3>توزيع أنواع الأجهزة</h3>
                        </div>
                        <div class="chart-container">
                            <canvas id="devicesChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                </div>
            </section>
            
            <section id="users-section" class="content-section admin-only">
                <div class="section-header">
                    <h1>إدارة المستخدمين</h1>
                    <div class="header-actions">
                        <div class="search-box">
                            <i class="fas fa-search"></i>
                            <input type="text" id="userSearch" placeholder="البحث في المستخدمين...">
                        </div>
                        <select id="roleFilter" class="form-control" style="width: 150px;">
                            <option value="">جميع الأدوار</option>
                            <option value="admin">مدير</option>
                            <option value="engineer">مهندس</option>
                            <option value="receptionist">موظف استقبال</option>
                        </select>
                        <button class="btn btn-primary" onclick="showAddUserModal()">
                            <i class="fas fa-plus"></i>
                            إضافة مستخدم جديد
                        </button>
                    </div>
                </div>

                <div class="users-stats">
                    <div class="stat-item">
                        <i class="fas fa-users"></i>
                        <div>
                            <span class="stat-number" id="totalUsersCount">0</span>
                            <span class="stat-label">إجمالي المستخدمين</span>
                        </div>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-user-check"></i>
                        <div>
                            <span class="stat-number" id="activeUsersCount">0</span>
                            <span class="stat-label">نشط</span>
                        </div>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-user-shield"></i>
                        <div>
                            <span class="stat-number" id="adminUsersCount">0</span>
                            <span class="stat-label">مدير</span>
                        </div>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-user-cog"></i>
                        <div>
                            <span class="stat-number" id="engineerUsersCount">0</span>
                            <span class="stat-label">مهندس</span>
                        </div>
                    </div>
                </div>

                <div class="section-content">
                    <div id="usersContainer" class="users-grid">
                        <!-- سيتم تحميل المستخدمين هنا -->
                    </div>
                </div>
            </section>
            
            <section id="settings-section" class="content-section">
                <div class="section-header">
                    <h1>الإعدادات</h1>
                </div>

                <div class="settings-grid">
                    <div class="settings-card">
                        <div class="settings-header">
                            <h3><i class="fas fa-user-circle"></i> الملف الشخصي</h3>
                        </div>
                        <div class="settings-content">
                            <form id="profileForm">
                                <div class="form-group">
                                    <label class="form-label">الاسم الكامل</label>
                                    <input type="text" id="profileFullName" class="form-control" readonly>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">اسم المستخدم</label>
                                    <input type="text" id="profileUsername" class="form-control" readonly>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">البريد الإلكتروني</label>
                                    <input type="email" id="profileEmail" class="form-control">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">رقم الهاتف</label>
                                    <input type="tel" id="profilePhone" class="form-control">
                                </div>
                                <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                            </form>
                        </div>
                    </div>

                    <div class="settings-card">
                        <div class="settings-header">
                            <h3><i class="fas fa-lock"></i> تغيير كلمة المرور</h3>
                        </div>
                        <div class="settings-content">
                            <form id="passwordForm">
                                <div class="form-group">
                                    <label class="form-label">كلمة المرور الحالية</label>
                                    <input type="password" id="currentPassword" class="form-control" required>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">كلمة المرور الجديدة</label>
                                    <input type="password" id="newPassword" class="form-control" required>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">تأكيد كلمة المرور الجديدة</label>
                                    <input type="password" id="confirmPassword" class="form-control" required>
                                </div>
                                <button type="submit" class="btn btn-primary">تغيير كلمة المرور</button>
                            </form>
                        </div>
                    </div>

                    <div class="settings-card">
                        <div class="settings-header">
                            <h3><i class="fas fa-cog"></i> إعدادات النظام</h3>
                        </div>
                        <div class="settings-content">
                            <div class="setting-item">
                                <div class="setting-info">
                                    <h4>الإشعارات</h4>
                                    <p>تفعيل الإشعارات للأجهزة والمخزون</p>
                                </div>
                                <label class="switch">
                                    <input type="checkbox" id="notificationsEnabled" checked>
                                    <span class="slider"></span>
                                </label>
                            </div>

                            <div class="setting-item">
                                <div class="setting-info">
                                    <h4>النسخ الاحتياطي التلقائي</h4>
                                    <p>إنشاء نسخة احتياطية يومياً</p>
                                </div>
                                <label class="switch">
                                    <input type="checkbox" id="autoBackup" checked>
                                    <span class="slider"></span>
                                </label>
                            </div>

                            <div class="setting-item">
                                <div class="setting-info">
                                    <h4>الوضع المظلم</h4>
                                    <p>تفعيل الوضع المظلم للواجهة</p>
                                </div>
                                <label class="switch">
                                    <input type="checkbox" id="darkMode">
                                    <span class="slider"></span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="settings-card">
                        <div class="settings-header">
                            <h3><i class="fas fa-database"></i> إدارة البيانات</h3>
                        </div>
                        <div class="settings-content">
                            <div class="data-action">
                                <h4>النسخ الاحتياطي</h4>
                                <p>إنشاء نسخة احتياطية من البيانات</p>
                                <button class="btn btn-outline" onclick="createBackup()">
                                    <i class="fas fa-download"></i>
                                    إنشاء نسخة احتياطية
                                </button>
                            </div>

                            <div class="data-action">
                                <h4>استعادة البيانات</h4>
                                <p>استعادة البيانات من نسخة احتياطية</p>
                                <button class="btn btn-outline" onclick="restoreBackup()">
                                    <i class="fas fa-upload"></i>
                                    استعادة البيانات
                                </button>
                            </div>

                            <div class="data-action">
                                <h4>تصدير البيانات</h4>
                                <p>تصدير البيانات إلى ملف Excel</p>
                                <button class="btn btn-outline" onclick="exportData()">
                                    <i class="fas fa-file-excel"></i>
                                    تصدير البيانات
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="settings-card">
                        <div class="settings-header">
                            <h3><i class="fas fa-info-circle"></i> معلومات النظام</h3>
                        </div>
                        <div class="settings-content">
                            <div class="info-item">
                                <span class="info-label">إصدار البرنامج:</span>
                                <span class="info-value">1.0.0</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">تاريخ آخر تحديث:</span>
                                <span class="info-value" id="lastUpdate">-</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">عدد المستخدمين:</span>
                                <span class="info-value" id="totalUsers">-</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">عدد الأجهزة:</span>
                                <span class="info-value" id="totalDevicesInfo">-</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">عدد العملاء:</span>
                                <span class="info-value" id="totalCustomersInfo">-</span>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>
    
    <!-- نافذة الإشعارات -->
    <div class="notification-panel" id="notificationPanel">
        <div class="notification-header">
            <h3>الإشعارات</h3>
            <button onclick="hideNotifications()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="notification-list">
            <div class="notification-item unread">
                <i class="fas fa-exclamation-triangle text-warning"></i>
                <div>
                    <p>قطعة غيار "شاشة iPhone 12" أوشكت على النفاد</p>
                    <small>منذ 5 دقائق</small>
                </div>
            </div>
            <div class="notification-item unread">
                <i class="fas fa-check-circle text-success"></i>
                <div>
                    <p>تم إصلاح جهاز "Samsung Galaxy S21" بنجاح</p>
                    <small>منذ 15 دقيقة</small>
                </div>
            </div>
            <div class="notification-item">
                <i class="fas fa-user-plus text-primary"></i>
                <div>
                    <p>تم إضافة عميل جديد "أحمد محمد"</p>
                    <small>منذ ساعة</small>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة إضافة/تعديل العميل -->
    <div class="modal" id="customerModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modalTitle">إضافة عميل جديد</h2>
                <button class="modal-close" onclick="hideCustomerModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="customerForm" class="modal-body">
                <div class="form-row">
                    <div class="form-group">
                        <label for="customerName" class="form-label">اسم العميل *</label>
                        <input type="text" id="customerName" name="name" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label for="customerPhone" class="form-label">رقم الهاتف *</label>
                        <input type="tel" id="customerPhone" name="phone" class="form-control" required>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="customerEmail" class="form-label">البريد الإلكتروني</label>
                        <input type="email" id="customerEmail" name="email" class="form-control">
                    </div>
                </div>

                <div class="form-group">
                    <label for="customerAddress" class="form-label">العنوان</label>
                    <textarea id="customerAddress" name="address" class="form-control" rows="3"></textarea>
                </div>

                <div class="form-group">
                    <label for="customerNotes" class="form-label">ملاحظات</label>
                    <textarea id="customerNotes" name="notes" class="form-control" rows="3"></textarea>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-outline" onclick="hideCustomerModal()">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ</button>
                </div>
            </form>
        </div>
    </div>

    <!-- نافذة تفاصيل العميل -->
    <div class="modal" id="customerDetailsModal">
        <div class="modal-content large">
            <div class="modal-header">
                <h2>تفاصيل العميل</h2>
                <button class="modal-close" onclick="hideCustomerDetailsModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="customerDetailsContent">
                <!-- سيتم تحميل المحتوى ديناميكياً -->
            </div>
        </div>
    </div>

    <!-- نافذة إضافة/تعديل الجهاز -->
    <div class="modal" id="deviceModal">
        <div class="modal-content large">
            <div class="modal-header">
                <h2 id="deviceModalTitle">إضافة جهاز جديد</h2>
                <button class="modal-close" onclick="hideDeviceModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="deviceForm" class="modal-body">
                <!-- معلومات العميل -->
                <div class="form-section">
                    <h3><i class="fas fa-user"></i> معلومات العميل</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="deviceCustomer" class="form-label">العميل *</label>
                            <select id="deviceCustomer" name="customer_id" class="form-control" required>
                                <option value="">اختر العميل...</option>
                            </select>
                        </div>
                    </div>
                    <div id="customerInfo" class="customer-info-display">
                        <!-- سيتم عرض معلومات العميل هنا -->
                    </div>
                </div>

                <!-- معلومات الجهاز -->
                <div class="form-section">
                    <h3><i class="fas fa-mobile-alt"></i> معلومات الجهاز</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="deviceType" class="form-label">نوع الجهاز *</label>
                            <select id="deviceType" name="device_type" class="form-control" required>
                                <option value="">اختر نوع الجهاز...</option>
                                <option value="هاتف">هاتف</option>
                                <option value="لابتوب">لابتوب</option>
                                <option value="تابلت">تابلت</option>
                                <option value="شاشة">شاشة</option>
                                <option value="ساعة ذكية">ساعة ذكية</option>
                                <option value="سماعة">سماعة</option>
                                <option value="أخرى">أخرى</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="deviceBrand" class="form-label">الماركة *</label>
                            <input type="text" id="deviceBrand" name="brand" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label for="deviceModel" class="form-label">الموديل *</label>
                            <input type="text" id="deviceModel" name="model" class="form-control" required>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="deviceColor" class="form-label">اللون</label>
                            <input type="text" id="deviceColor" name="color" class="form-control">
                        </div>
                        <div class="form-group">
                            <label for="serialNumber" class="form-label">الرقم التسلسلي</label>
                            <input type="text" id="serialNumber" name="serial_number" class="form-control">
                        </div>
                        <div class="form-group">
                            <label for="devicePassword" class="form-label">كلمة المرور/النمط</label>
                            <input type="text" id="devicePassword" name="password" class="form-control">
                        </div>
                    </div>
                </div>

                <!-- وصف المشكلة -->
                <div class="form-section">
                    <h3><i class="fas fa-exclamation-triangle"></i> وصف المشكلة</h3>
                    <div class="form-group">
                        <label for="problemDescription" class="form-label">تفاصيل العطل *</label>
                        <textarea id="problemDescription" name="problem_description" class="form-control" rows="4" required></textarea>
                    </div>
                    <div class="form-group">
                        <label for="accessories" class="form-label">الملحقات المصاحبة</label>
                        <textarea id="accessories" name="accessories" class="form-control" rows="2" placeholder="مثال: شاحن، كابل، سماعة، جراب..."></textarea>
                    </div>
                </div>

                <!-- التواريخ والتكاليف -->
                <div class="form-section">
                    <h3><i class="fas fa-calendar-alt"></i> التواريخ والتكاليف</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="receivedDate" class="form-label">تاريخ الاستلام *</label>
                            <input type="date" id="receivedDate" name="received_date" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label for="expectedDeliveryDate" class="form-label">التسليم المتوقع</label>
                            <input type="date" id="expectedDeliveryDate" name="expected_delivery_date" class="form-control">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="inspectionFee" class="form-label">رسوم الكشف (ريال)</label>
                            <input type="number" id="inspectionFee" name="inspection_fee" class="form-control" min="0" step="0.01">
                        </div>
                        <div class="form-group">
                            <label for="estimatedCost" class="form-label">التكلفة المتوقعة (ريال)</label>
                            <input type="number" id="estimatedCost" name="estimated_cost" class="form-control" min="0" step="0.01">
                        </div>
                    </div>
                </div>

                <!-- المهندس المسؤول -->
                <div class="form-section">
                    <h3><i class="fas fa-user-cog"></i> المهندس المسؤول</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="assignedEngineer" class="form-label">المهندس المسؤول</label>
                            <select id="assignedEngineer" name="assigned_engineer_id" class="form-control">
                                <option value="">اختر المهندس...</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- ملاحظات -->
                <div class="form-section">
                    <h3><i class="fas fa-sticky-note"></i> ملاحظات</h3>
                    <div class="form-group">
                        <label for="deviceNotes" class="form-label">ملاحظات إضافية</label>
                        <textarea id="deviceNotes" name="notes" class="form-control" rows="3"></textarea>
                    </div>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-outline" onclick="hideDeviceModal()">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ الجهاز</button>
                </div>
            </form>
        </div>
    </div>

    <!-- نافذة إضافة/تعديل المستخدم -->
    <div class="modal" id="userModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="userModalTitle">إضافة مستخدم جديد</h2>
                <button class="modal-close" onclick="hideUserModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="userForm" class="modal-body">
                <div class="form-row">
                    <div class="form-group">
                        <label for="userUsername" class="form-label">اسم المستخدم *</label>
                        <input type="text" id="userUsername" name="username" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label for="userFullName" class="form-label">الاسم الكامل *</label>
                        <input type="text" id="userFullName" name="full_name" class="form-control" required>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="userRole" class="form-label">الدور *</label>
                        <select id="userRole" name="role" class="form-control" required>
                            <option value="">اختر الدور...</option>
                            <option value="admin">مدير</option>
                            <option value="engineer">مهندس</option>
                            <option value="receptionist">موظف استقبال</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="userEmail" class="form-label">البريد الإلكتروني</label>
                        <input type="email" id="userEmail" name="email" class="form-control">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="userPhone" class="form-label">رقم الهاتف</label>
                        <input type="tel" id="userPhone" name="phone" class="form-control">
                    </div>
                    <div class="form-group" id="passwordGroup">
                        <label for="userPassword" class="form-label">كلمة المرور *</label>
                        <input type="password" id="userPassword" name="password" class="form-control" required>
                    </div>
                </div>

                <div class="form-group">
                    <label class="checkbox-container">
                        <input type="checkbox" id="userActive" name="is_active" checked>
                        <span class="checkmark"></span>
                        حساب نشط
                    </label>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-outline" onclick="hideUserModal()">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ</button>
                </div>
            </form>
        </div>
    </div>

    <!-- نافذة جلسات المستخدم -->
    <div class="modal" id="userSessionsModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>جلسات المستخدم</h2>
                <button class="modal-close" onclick="hideUserSessionsModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="userSessionsContent">
                <!-- سيتم تحميل المحتوى ديناميكياً -->
            </div>
        </div>
    </div>

    <!-- نافذة إضافة/تعديل قطعة الغيار -->
    <div class="modal" id="sparePartModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="sparePartModalTitle">إضافة قطعة غيار جديدة</h2>
                <button class="modal-close" onclick="hideSparePartModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="sparePartForm" class="modal-body">
                <div class="form-row">
                    <div class="form-group">
                        <label for="sparePartName" class="form-label">اسم قطعة الغيار *</label>
                        <input type="text" id="sparePartName" name="name" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label for="sparePartCategory" class="form-label">الفئة *</label>
                        <select id="sparePartCategory" name="category" class="form-control" required>
                            <option value="">اختر الفئة...</option>
                            <option value="شاشات">شاشات</option>
                            <option value="بطاريات">بطاريات</option>
                            <option value="كابلات">كابلات</option>
                            <option value="شواحن">شواحن</option>
                            <option value="سماعات">سماعات</option>
                            <option value="كاميرات">كاميرات</option>
                            <option value="أزرار">أزرار</option>
                            <option value="مكبرات صوت">مكبرات صوت</option>
                            <option value="أخرى">أخرى</option>
                        </select>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="sparePartBrand" class="form-label">الماركة</label>
                        <input type="text" id="sparePartBrand" name="brand" class="form-control">
                    </div>
                    <div class="form-group">
                        <label for="sparePartModel" class="form-label">الموديل</label>
                        <input type="text" id="sparePartModel" name="model" class="form-control">
                    </div>
                    <div class="form-group">
                        <label for="partNumber" class="form-label">رقم القطعة</label>
                        <input type="text" id="partNumber" name="part_number" class="form-control">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="costPrice" class="form-label">سعر الشراء (ريال) *</label>
                        <input type="number" id="costPrice" name="cost_price" class="form-control" min="0" step="0.01" required>
                    </div>
                    <div class="form-group">
                        <label for="sellingPrice" class="form-label">سعر البيع (ريال) *</label>
                        <input type="number" id="sellingPrice" name="selling_price" class="form-control" min="0" step="0.01" required>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="quantityInStock" class="form-label">الكمية في المخزون *</label>
                        <input type="number" id="quantityInStock" name="quantity_in_stock" class="form-control" min="0" required>
                    </div>
                    <div class="form-group">
                        <label for="minimumStockLevel" class="form-label">الحد الأدنى للمخزون *</label>
                        <input type="number" id="minimumStockLevel" name="minimum_stock_level" class="form-control" min="1" value="5" required>
                    </div>
                </div>

                <div class="form-group">
                    <label for="supplier" class="form-label">المورد</label>
                    <input type="text" id="supplier" name="supplier" class="form-control">
                </div>

                <div class="form-group">
                    <label for="sparePartNotes" class="form-label">ملاحظات</label>
                    <textarea id="sparePartNotes" name="notes" class="form-control" rows="3"></textarea>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-outline" onclick="hideSparePartModal()">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ</button>
                </div>
            </form>
        </div>
    </div>

    <script src="../js/dashboard.js"></script>
    <script src="../js/customers.js"></script>
    <script src="../js/devices.js"></script>
    <script src="../js/users.js"></script>
    <script src="../js/spare-parts.js"></script>
    <script src="../js/reports.js"></script>
    <script src="../js/settings.js"></script>
    <script src="../js/sample-data.js"></script>
    <script src="../js/login.js"></script>
</body>
</html>
