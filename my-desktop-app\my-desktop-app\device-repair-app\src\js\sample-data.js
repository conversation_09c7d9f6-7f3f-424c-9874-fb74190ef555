const { ipc<PERSON>ender<PERSON> } = require('electron');

// إضافة بيانات تجريبية للنظام
async function addSampleData() {
    try {
        console.log('بدء إضافة البيانات التجريبية...');
        
        // إضافة عملاء تجريبيين
        await addSampleCustomers();
        
        // إضافة مهندسين تجريبيين
        await addSampleEngineers();
        
        // إضافة قطع غيار تجريبية
        await addSampleSpareParts();
        
        // إضافة أجهزة تجريبية
        await addSampleDevices();
        
        console.log('تم إضافة البيانات التجريبية بنجاح');
        
    } catch (error) {
        console.error('خطأ في إضافة البيانات التجريبية:', error);
    }
}

// إضافة عملاء تجريبيين
async function addSampleCustomers() {
    const customers = [
        {
            name: 'أحم<PERSON> محمد علي',
            phone: '0501234567',
            email: '<EMAIL>',
            address: 'الرياض، حي النخيل'
        },
        {
            name: 'فاطمة عبدالله',
            phone: '0509876543',
            email: '<EMAIL>',
            address: 'جدة، حي الصفا'
        },
        {
            name: 'محمد سعد الدين',
            phone: '0551122334',
            email: '<EMAIL>',
            address: 'الدمام، حي الفيصلية'
        },
        {
            name: 'نورا أحمد',
            phone: '0556677889',
            email: '<EMAIL>',
            address: 'مكة المكرمة، حي العزيزية'
        },
        {
            name: 'خالد عبدالعزيز',
            phone: '0544332211',
            email: '<EMAIL>',
            address: 'المدينة المنورة، حي قباء'
        }
    ];
    
    for (const customer of customers) {
        // التحقق من عدم وجود العميل مسبقاً
        const existing = await ipcRenderer.invoke('database-get',
            'SELECT id FROM customers WHERE phone = ?',
            [customer.phone]
        );
        
        if (!existing) {
            await ipcRenderer.invoke('database-run',
                'INSERT INTO customers (name, phone, email, address) VALUES (?, ?, ?, ?)',
                [customer.name, customer.phone, customer.email, customer.address]
            );
        }
    }
}

// إضافة مهندسين تجريبيين
async function addSampleEngineers() {
    const bcrypt = require('bcrypt');
    
    const engineers = [
        {
            username: 'eng_omar',
            password: 'engineer123',
            full_name: 'عمر حسن المهندس',
            role: 'engineer',
            email: '<EMAIL>',
            phone: '0501111111'
        },
        {
            username: 'eng_sara',
            password: 'engineer123',
            full_name: 'سارة أحمد المهندسة',
            role: 'engineer',
            email: '<EMAIL>',
            phone: '0502222222'
        },
        {
            username: 'receptionist',
            password: 'reception123',
            full_name: 'ليلى محمد موظفة الاستقبال',
            role: 'receptionist',
            email: '<EMAIL>',
            phone: '0503333333'
        }
    ];
    
    for (const engineer of engineers) {
        // التحقق من عدم وجود المستخدم مسبقاً
        const existing = await ipcRenderer.invoke('database-get',
            'SELECT id FROM users WHERE username = ?',
            [engineer.username]
        );
        
        if (!existing) {
            const hashedPassword = await bcrypt.hash(engineer.password, 10);
            await ipcRenderer.invoke('database-run',
                'INSERT INTO users (username, password, full_name, role, email, phone) VALUES (?, ?, ?, ?, ?, ?)',
                [engineer.username, hashedPassword, engineer.full_name, engineer.role, engineer.email, engineer.phone]
            );
        }
    }
}

// إضافة قطع غيار تجريبية
async function addSampleSpareParts() {
    const spareParts = [
        {
            name: 'شاشة iPhone 12',
            category: 'شاشات',
            brand: 'Apple',
            model: 'iPhone 12',
            part_number: 'IP12-LCD-001',
            cost_price: 450.00,
            selling_price: 650.00,
            quantity_in_stock: 15,
            minimum_stock_level: 5,
            supplier: 'شركة قطع الغيار المتقدمة'
        },
        {
            name: 'بطارية Samsung Galaxy S21',
            category: 'بطاريات',
            brand: 'Samsung',
            model: 'Galaxy S21',
            part_number: 'SGS21-BAT-001',
            cost_price: 120.00,
            selling_price: 180.00,
            quantity_in_stock: 25,
            minimum_stock_level: 10,
            supplier: 'مورد البطاريات الأصلية'
        },
        {
            name: 'كابل شحن Type-C',
            category: 'كابلات',
            brand: 'Universal',
            model: 'Type-C',
            part_number: 'USBC-CBL-001',
            cost_price: 25.00,
            selling_price: 45.00,
            quantity_in_stock: 50,
            minimum_stock_level: 20,
            supplier: 'شركة الكابلات العالمية'
        },
        {
            name: 'شاحن لاسلكي',
            category: 'شواحن',
            brand: 'Universal',
            model: 'Wireless 15W',
            part_number: 'WRL-CHG-15W',
            cost_price: 80.00,
            selling_price: 120.00,
            quantity_in_stock: 12,
            minimum_stock_level: 8,
            supplier: 'مورد الشواحن اللاسلكية'
        },
        {
            name: 'سماعة أذن بلوتوث',
            category: 'سماعات',
            brand: 'Universal',
            model: 'BT-Earbuds',
            part_number: 'BT-EAR-001',
            cost_price: 150.00,
            selling_price: 220.00,
            quantity_in_stock: 8,
            minimum_stock_level: 5,
            supplier: 'شركة السماعات الذكية'
        },
        {
            name: 'كاميرا خلفية iPhone 13',
            category: 'كاميرات',
            brand: 'Apple',
            model: 'iPhone 13',
            part_number: 'IP13-CAM-REAR',
            cost_price: 350.00,
            selling_price: 500.00,
            quantity_in_stock: 3,
            minimum_stock_level: 5,
            supplier: 'مورد قطع آبل الأصلية'
        }
    ];
    
    for (const part of spareParts) {
        // التحقق من عدم وجود القطعة مسبقاً
        const existing = await ipcRenderer.invoke('database-get',
            'SELECT id FROM spare_parts WHERE part_number = ?',
            [part.part_number]
        );
        
        if (!existing) {
            await ipcRenderer.invoke('database-run',
                `INSERT INTO spare_parts (
                    name, category, brand, model, part_number, cost_price,
                    selling_price, quantity_in_stock, minimum_stock_level, supplier
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                [
                    part.name, part.category, part.brand, part.model, part.part_number,
                    part.cost_price, part.selling_price, part.quantity_in_stock,
                    part.minimum_stock_level, part.supplier
                ]
            );
        }
    }
}

// إضافة أجهزة تجريبية
async function addSampleDevices() {
    // الحصول على العملاء والمهندسين
    const customers = await ipcRenderer.invoke('database-all', 'SELECT id FROM customers LIMIT 5');
    const engineers = await ipcRenderer.invoke('database-all', 'SELECT id FROM users WHERE role = "engineer" LIMIT 2');
    
    if (customers.length === 0 || engineers.length === 0) {
        console.log('لا توجد عملاء أو مهندسين لإضافة الأجهزة');
        return;
    }
    
    const devices = [
        {
            customer_id: customers[0].id,
            device_type: 'هاتف',
            brand: 'Apple',
            model: 'iPhone 12',
            color: 'أزرق',
            serial_number: 'IP12BL123456',
            password: '1234',
            problem_description: 'الشاشة مكسورة والجهاز لا يستجيب للمس',
            accessories: 'شاحن، كابل، سماعة',
            received_date: new Date().toISOString(),
            inspection_fee: 50.00,
            estimated_cost: 700.00,
            expected_delivery_date: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            assigned_engineer_id: engineers[0].id,
            status: 'repairing'
        },
        {
            customer_id: customers[1].id,
            device_type: 'لابتوب',
            brand: 'HP',
            model: 'Pavilion 15',
            color: 'فضي',
            serial_number: 'HP15SV789012',
            password: 'laptop123',
            problem_description: 'الجهاز لا يشتغل والشاشة سوداء',
            accessories: 'شاحن، حقيبة',
            received_date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
            inspection_fee: 100.00,
            estimated_cost: 800.00,
            expected_delivery_date: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            assigned_engineer_id: engineers[1].id,
            status: 'inspecting'
        },
        {
            customer_id: customers[2].id,
            device_type: 'هاتف',
            brand: 'Samsung',
            model: 'Galaxy S21',
            color: 'أسود',
            serial_number: 'SGS21BK345678',
            password: '0000',
            problem_description: 'البطارية تنفد بسرعة والجهاز يسخن',
            accessories: 'شاحن لاسلكي، كابل',
            received_date: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
            inspection_fee: 50.00,
            estimated_cost: 250.00,
            expected_delivery_date: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            assigned_engineer_id: engineers[0].id,
            status: 'completed',
            final_cost: 220.00
        },
        {
            customer_id: customers[3].id,
            device_type: 'تابلت',
            brand: 'iPad',
            model: 'Air 4',
            color: 'وردي',
            serial_number: 'IPADAIR4PK901',
            password: 'ipad2023',
            problem_description: 'الشاشة بها خطوط ملونة والصوت لا يعمل',
            accessories: 'قلم، شاحن، كيبورد',
            received_date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
            inspection_fee: 75.00,
            estimated_cost: 900.00,
            expected_delivery_date: new Date(Date.now() + 1 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            assigned_engineer_id: engineers[1].id,
            status: 'delivered',
            final_cost: 850.00,
            actual_delivery_date: new Date().toISOString()
        }
    ];
    
    for (const device of devices) {
        // التحقق من عدم وجود الجهاز مسبقاً
        const existing = await ipcRenderer.invoke('database-get',
            'SELECT id FROM devices WHERE serial_number = ?',
            [device.serial_number]
        );
        
        if (!existing) {
            const result = await ipcRenderer.invoke('database-run',
                `INSERT INTO devices (
                    customer_id, device_type, brand, model, color, serial_number,
                    password, problem_description, accessories, received_date,
                    inspection_fee, estimated_cost, final_cost, expected_delivery_date,
                    actual_delivery_date, assigned_engineer_id, status
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                [
                    device.customer_id, device.device_type, device.brand, device.model,
                    device.color, device.serial_number, device.password, device.problem_description,
                    device.accessories, device.received_date, device.inspection_fee,
                    device.estimated_cost, device.final_cost || null, device.expected_delivery_date,
                    device.actual_delivery_date || null, device.assigned_engineer_id, device.status
                ]
            );
            
            // إضافة سجل حالة الجهاز
            await ipcRenderer.invoke('database-run',
                'INSERT INTO device_status_log (device_id, new_status, changed_by, notes) VALUES (?, ?, ?, ?)',
                [result.id, device.status, device.assigned_engineer_id, `تم تعيين الحالة: ${device.status}`]
            );
        }
    }
}

// تصدير الدالة
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { addSampleData };
}

// تشغيل إضافة البيانات التجريبية عند تحميل الصفحة (للاختبار فقط)
if (typeof window !== 'undefined') {
    window.addSampleData = addSampleData;
}
