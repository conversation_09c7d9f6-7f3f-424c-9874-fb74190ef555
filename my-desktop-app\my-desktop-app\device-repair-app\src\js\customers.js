const { ipc<PERSON>ender<PERSON> } = require('electron');

// متغيرات عامة
let customers = [];
let currentCustomer = null;
let isEditMode = false;

// تهيئة صفحة العملاء
function initializeCustomers() {
    loadCustomers();
    setupEventListeners();
}

// إعداد مستمعي الأحداث
function setupEventListeners() {
    // نموذج إضافة/تعديل العميل
    const customerForm = document.getElementById('customerForm');
    if (customerForm) {
        customerForm.addEventListener('submit', handleCustomerSubmit);
    }
    
    // البحث
    const searchInput = document.getElementById('customerSearch');
    if (searchInput) {
        searchInput.addEventListener('input', handleCustomerSearch);
    }
}

// تحميل العملاء
async function loadCustomers() {
    try {
        customers = await ipcRenderer.invoke('database-all', 
            'SELECT * FROM customers ORDER BY created_at DESC'
        );
        
        displayCustomers(customers);
        updateCustomersCount();
        
    } catch (error) {
        console.error('خطأ في تحميل العملاء:', error);
        showNotification('حدث خطأ في تحميل العملاء', 'error');
    }
}

// عرض العملاء
function displayCustomers(customersToShow) {
    const container = document.getElementById('customersContainer');
    if (!container) return;
    
    container.innerHTML = '';
    
    if (customersToShow.length === 0) {
        container.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-users"></i>
                <h3>لا يوجد عملاء</h3>
                <p>ابدأ بإضافة عميل جديد</p>
                <button class="btn btn-primary" onclick="showAddCustomerModal()">
                    <i class="fas fa-plus"></i>
                    إضافة عميل جديد
                </button>
            </div>
        `;
        return;
    }
    
    customersToShow.forEach(customer => {
        const customerCard = createCustomerCard(customer);
        container.appendChild(customerCard);
    });
}

// إنشاء بطاقة عميل
function createCustomerCard(customer) {
    const div = document.createElement('div');
    div.className = 'customer-card';
    div.innerHTML = `
        <div class="customer-header">
            <div class="customer-avatar">
                <i class="fas fa-user"></i>
            </div>
            <div class="customer-info">
                <h3>${customer.name}</h3>
                <p><i class="fas fa-phone"></i> ${customer.phone}</p>
                ${customer.email ? `<p><i class="fas fa-envelope"></i> ${customer.email}</p>` : ''}
            </div>
            <div class="customer-actions">
                <button class="btn-icon" onclick="viewCustomer(${customer.id})" title="عرض التفاصيل">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="btn-icon" onclick="editCustomer(${customer.id})" title="تعديل">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn-icon btn-danger" onclick="deleteCustomer(${customer.id})" title="حذف">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
        <div class="customer-details">
            ${customer.address ? `<p><i class="fas fa-map-marker-alt"></i> ${customer.address}</p>` : ''}
            <p><i class="fas fa-calendar"></i> تاريخ التسجيل: ${formatDate(customer.created_at)}</p>
        </div>
        <div class="customer-stats">
            <div class="stat-item">
                <span class="stat-number" id="devices-count-${customer.id}">0</span>
                <span class="stat-label">الأجهزة</span>
            </div>
            <div class="stat-item">
                <span class="stat-number" id="total-spent-${customer.id}">0</span>
                <span class="stat-label">إجمالي الإنفاق</span>
            </div>
        </div>
    `;
    
    // تحميل إحصائيات العميل
    loadCustomerStats(customer.id);
    
    return div;
}

// تحميل إحصائيات العميل
async function loadCustomerStats(customerId) {
    try {
        // عدد الأجهزة
        const devicesCount = await ipcRenderer.invoke('database-get',
            'SELECT COUNT(*) as count FROM devices WHERE customer_id = ?',
            [customerId]
        );
        
        // إجمالي الإنفاق
        const totalSpent = await ipcRenderer.invoke('database-get',
            'SELECT SUM(final_cost) as total FROM devices WHERE customer_id = ? AND status = "delivered"',
            [customerId]
        );
        
        // تحديث الواجهة
        const devicesElement = document.getElementById(`devices-count-${customerId}`);
        const spentElement = document.getElementById(`total-spent-${customerId}`);
        
        if (devicesElement) {
            devicesElement.textContent = devicesCount?.count || 0;
        }
        
        if (spentElement) {
            spentElement.textContent = formatCurrency(totalSpent?.total || 0);
        }
        
    } catch (error) {
        console.error('خطأ في تحميل إحصائيات العميل:', error);
    }
}

// إظهار نافذة إضافة عميل
function showAddCustomerModal() {
    isEditMode = false;
    currentCustomer = null;

    const modal = document.getElementById('customerModal');
    const form = document.getElementById('customerForm');
    const title = document.getElementById('modalTitle');

    if (modal && form && title) {
        title.textContent = 'إضافة عميل جديد';
        form.reset();
        modal.classList.add('show');
    }
}

// جعل الدالة متاحة عالمياً
window.showAddCustomerModal = showAddCustomerModal;

// عرض تفاصيل العميل
async function viewCustomer(customerId) {
    try {
        const customer = await ipcRenderer.invoke('database-get',
            'SELECT * FROM customers WHERE id = ?',
            [customerId]
        );
        
        if (!customer) {
            showNotification('العميل غير موجود', 'error');
            return;
        }
        
        // تحميل أجهزة العميل
        const devices = await ipcRenderer.invoke('database-all',
            'SELECT * FROM devices WHERE customer_id = ? ORDER BY created_at DESC',
            [customerId]
        );
        
        showCustomerDetailsModal(customer, devices);
        
    } catch (error) {
        console.error('خطأ في عرض تفاصيل العميل:', error);
        showNotification('حدث خطأ في عرض تفاصيل العميل', 'error');
    }
}

// إظهار نافذة تفاصيل العميل
function showCustomerDetailsModal(customer, devices) {
    const modal = document.getElementById('customerDetailsModal');
    const content = document.getElementById('customerDetailsContent');
    
    content.innerHTML = `
        <div class="customer-profile">
            <div class="profile-header">
                <div class="profile-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <div class="profile-info">
                    <h2>${customer.name}</h2>
                    <p><i class="fas fa-phone"></i> ${customer.phone}</p>
                    ${customer.email ? `<p><i class="fas fa-envelope"></i> ${customer.email}</p>` : ''}
                    ${customer.address ? `<p><i class="fas fa-map-marker-alt"></i> ${customer.address}</p>` : ''}
                </div>
            </div>
            
            <div class="profile-stats">
                <div class="stat-card">
                    <h3>${devices.length}</h3>
                    <p>إجمالي الأجهزة</p>
                </div>
                <div class="stat-card">
                    <h3>${devices.filter(d => d.status === 'delivered').length}</h3>
                    <p>أجهزة مُسلمة</p>
                </div>
                <div class="stat-card">
                    <h3>${formatCurrency(devices.filter(d => d.status === 'delivered').reduce((sum, d) => sum + (d.final_cost || 0), 0))}</h3>
                    <p>إجمالي الإنفاق</p>
                </div>
            </div>
            
            <div class="devices-history">
                <h3>سجل الأجهزة</h3>
                ${devices.length > 0 ? createDevicesTable(devices) : '<p class="text-muted">لا توجد أجهزة مسجلة</p>'}
            </div>
        </div>
    `;
    
    modal.classList.add('show');
}

// إنشاء جدول الأجهزة
function createDevicesTable(devices) {
    let table = `
        <table class="table">
            <thead>
                <tr>
                    <th>نوع الجهاز</th>
                    <th>الماركة والموديل</th>
                    <th>الحالة</th>
                    <th>التكلفة</th>
                    <th>تاريخ الاستلام</th>
                </tr>
            </thead>
            <tbody>
    `;
    
    devices.forEach(device => {
        table += `
            <tr>
                <td>${device.device_type}</td>
                <td>${device.brand} ${device.model}</td>
                <td><span class="status-badge status-${device.status}">${getStatusDisplayName(device.status)}</span></td>
                <td>${formatCurrency(device.final_cost || 0)}</td>
                <td>${formatDate(device.received_date)}</td>
            </tr>
        `;
    });
    
    table += '</tbody></table>';
    return table;
}

// تعديل العميل
async function editCustomer(customerId) {
    try {
        const customer = await ipcRenderer.invoke('database-get',
            'SELECT * FROM customers WHERE id = ?',
            [customerId]
        );
        
        if (!customer) {
            showNotification('العميل غير موجود', 'error');
            return;
        }
        
        isEditMode = true;
        currentCustomer = customer;
        
        const modal = document.getElementById('customerModal');
        const form = document.getElementById('customerForm');
        const title = document.getElementById('modalTitle');
        
        title.textContent = 'تعديل بيانات العميل';
        
        // ملء النموذج بالبيانات الحالية
        document.getElementById('customerName').value = customer.name;
        document.getElementById('customerPhone').value = customer.phone;
        document.getElementById('customerEmail').value = customer.email || '';
        document.getElementById('customerAddress').value = customer.address || '';
        document.getElementById('customerNotes').value = customer.notes || '';
        
        modal.classList.add('show');
        
    } catch (error) {
        console.error('خطأ في تحميل بيانات العميل للتعديل:', error);
        showNotification('حدث خطأ في تحميل بيانات العميل', 'error');
    }
}

// حذف العميل
async function deleteCustomer(customerId) {
    if (!confirm('هل أنت متأكد من حذف هذا العميل؟ سيتم حذف جميع أجهزته أيضاً.')) {
        return;
    }
    
    try {
        // التحقق من وجود أجهزة للعميل
        const devicesCount = await ipcRenderer.invoke('database-get',
            'SELECT COUNT(*) as count FROM devices WHERE customer_id = ?',
            [customerId]
        );
        
        if (devicesCount.count > 0) {
            if (!confirm(`هذا العميل لديه ${devicesCount.count} جهاز. هل تريد المتابعة؟`)) {
                return;
            }
            
            // حذف أجهزة العميل أولاً
            await ipcRenderer.invoke('database-run',
                'DELETE FROM devices WHERE customer_id = ?',
                [customerId]
            );
        }
        
        // حذف العميل
        await ipcRenderer.invoke('database-run',
            'DELETE FROM customers WHERE id = ?',
            [customerId]
        );
        
        showNotification('تم حذف العميل بنجاح', 'success');
        loadCustomers();
        
    } catch (error) {
        console.error('خطأ في حذف العميل:', error);
        showNotification('حدث خطأ في حذف العميل', 'error');
    }
}

// معالج إرسال نموذج العميل
async function handleCustomerSubmit(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const customerData = {
        name: formData.get('name').trim(),
        phone: formData.get('phone').trim(),
        email: formData.get('email').trim(),
        address: formData.get('address').trim(),
        notes: formData.get('notes').trim()
    };
    
    // التحقق من صحة البيانات
    if (!validateCustomerData(customerData)) {
        return;
    }
    
    try {
        if (isEditMode && currentCustomer) {
            // تحديث العميل
            await ipcRenderer.invoke('database-run',
                'UPDATE customers SET name = ?, phone = ?, email = ?, address = ?, notes = ?, updated_at = ? WHERE id = ?',
                [customerData.name, customerData.phone, customerData.email, customerData.address, customerData.notes, new Date().toISOString(), currentCustomer.id]
            );
            
            showNotification('تم تحديث بيانات العميل بنجاح', 'success');
        } else {
            // إضافة عميل جديد
            await ipcRenderer.invoke('database-run',
                'INSERT INTO customers (name, phone, email, address, notes) VALUES (?, ?, ?, ?, ?)',
                [customerData.name, customerData.phone, customerData.email, customerData.address, customerData.notes]
            );
            
            showNotification('تم إضافة العميل بنجاح', 'success');
        }
        
        hideCustomerModal();
        loadCustomers();
        
    } catch (error) {
        console.error('خطأ في حفظ بيانات العميل:', error);
        showNotification('حدث خطأ في حفظ بيانات العميل', 'error');
    }
}

// التحقق من صحة بيانات العميل
function validateCustomerData(data) {
    if (!data.name) {
        showNotification('يرجى إدخال اسم العميل', 'error');
        return false;
    }
    
    if (!data.phone) {
        showNotification('يرجى إدخال رقم الهاتف', 'error');
        return false;
    }
    
    // التحقق من صحة رقم الهاتف
    const phoneRegex = /^[0-9+\-\s()]+$/;
    if (!phoneRegex.test(data.phone)) {
        showNotification('رقم الهاتف غير صحيح', 'error');
        return false;
    }
    
    // التحقق من صحة البريد الإلكتروني إذا تم إدخاله
    if (data.email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(data.email)) {
            showNotification('البريد الإلكتروني غير صحيح', 'error');
            return false;
        }
    }
    
    return true;
}

// البحث في العملاء
function handleCustomerSearch(e) {
    const searchTerm = e.target.value.toLowerCase().trim();
    
    if (!searchTerm) {
        displayCustomers(customers);
        return;
    }
    
    const filteredCustomers = customers.filter(customer => 
        customer.name.toLowerCase().includes(searchTerm) ||
        customer.phone.includes(searchTerm) ||
        (customer.email && customer.email.toLowerCase().includes(searchTerm))
    );
    
    displayCustomers(filteredCustomers);
}

// إخفاء نافذة العميل
function hideCustomerModal() {
    const modal = document.getElementById('customerModal');
    modal.classList.remove('show');
}

// إخفاء نافذة تفاصيل العميل
function hideCustomerDetailsModal() {
    const modal = document.getElementById('customerDetailsModal');
    modal.classList.remove('show');
}

// تحديث عدد العملاء
function updateCustomersCount() {
    const countElement = document.getElementById('customersCount');
    if (countElement) {
        countElement.textContent = customers.length;
    }
}

// دوال مساعدة
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA');
}

function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-SA', {
        style: 'decimal',
        minimumFractionDigits: 0,
        maximumFractionDigits: 2
    }).format(amount || 0) + ' ريال';
}

function getStatusDisplayName(status) {
    const statuses = {
        'received': 'مستلم',
        'inspecting': 'قيد الفحص',
        'repairing': 'قيد الإصلاح',
        'completed': 'مكتمل',
        'delivered': 'مُسلم',
        'cancelled': 'ملغي'
    };
    return statuses[status] || status;
}

function showNotification(message, type = 'info') {
    // يمكن تحسين هذا لاحقاً بنظام إشعارات أفضل
    if (type === 'error') {
        alert('خطأ: ' + message);
    } else if (type === 'success') {
        alert('نجح: ' + message);
    } else {
        alert(message);
    }
}

// تصدير الدوال
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        initializeCustomers,
        showAddCustomerModal,
        viewCustomer,
        editCustomer,
        deleteCustomer,
        hideCustomerModal,
        hideCustomerDetailsModal
    };
}
