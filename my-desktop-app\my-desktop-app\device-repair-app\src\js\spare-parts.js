const { ipcRenderer } = require('electron');

// متغيرات عامة
let spareParts = [];
let currentSparePart = null;
let isEditMode = false;

// تهيئة صفحة قطع الغيار
function initializeSpareParts() {
    loadSpareParts();
    setupSparePartsEventListeners();
}

// إعداد مستمعي الأحداث
function setupSparePartsEventListeners() {
    // نموذج إضافة/تعديل قطعة الغيار
    const sparePartForm = document.getElementById('sparePartForm');
    if (sparePartForm) {
        sparePartForm.addEventListener('submit', handleSparePartSubmit);
    }
    
    // البحث
    const searchInput = document.getElementById('sparePartSearch');
    if (searchInput) {
        searchInput.addEventListener('input', handleSparePartSearch);
    }
    
    // فلترة حسب الفئة
    const categoryFilter = document.getElementById('categoryFilter');
    if (categoryFilter) {
        categoryFilter.addEventListener('change', handleCategoryFilter);
    }
    
    // فلترة حسب المخزون
    const stockFilter = document.getElementById('stockFilter');
    if (stockFilter) {
        stockFilter.addEventListener('change', handleStockFilter);
    }
}

// تحميل قطع الغيار
async function loadSpareParts() {
    try {
        spareParts = await ipcRenderer.invoke('database-all', 
            'SELECT * FROM spare_parts ORDER BY created_at DESC'
        );
        
        displaySpareParts(spareParts);
        updateSparePartsStats();
        
    } catch (error) {
        console.error('خطأ في تحميل قطع الغيار:', error);
        showNotification('حدث خطأ في تحميل قطع الغيار', 'error');
    }
}

// عرض قطع الغيار
function displaySpareParts(sparePartsToShow) {
    const container = document.getElementById('sparePartsContainer');
    if (!container) return;
    
    container.innerHTML = '';
    
    if (sparePartsToShow.length === 0) {
        container.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-cogs"></i>
                <h3>لا توجد قطع غيار</h3>
                <p>ابدأ بإضافة قطعة غيار جديدة</p>
                <button class="btn btn-primary" onclick="showAddSparePartModal()">
                    <i class="fas fa-plus"></i>
                    إضافة قطعة غيار جديدة
                </button>
            </div>
        `;
        return;
    }
    
    sparePartsToShow.forEach(sparePart => {
        const sparePartCard = createSparePartCard(sparePart);
        container.appendChild(sparePartCard);
    });
}

// إنشاء بطاقة قطعة غيار
function createSparePartCard(sparePart) {
    const div = document.createElement('div');
    div.className = 'spare-part-card';
    
    const stockStatus = getStockStatus(sparePart);
    const stockClass = getStockClass(sparePart);
    
    div.innerHTML = `
        <div class="spare-part-header">
            <div class="spare-part-icon">
                <i class="fas ${getCategoryIcon(sparePart.category)}"></i>
            </div>
            <div class="spare-part-info">
                <h3>${sparePart.name}</h3>
                <p><i class="fas fa-tag"></i> ${sparePart.category}</p>
                ${sparePart.brand ? `<p><i class="fas fa-industry"></i> ${sparePart.brand}</p>` : ''}
                ${sparePart.part_number ? `<p><i class="fas fa-barcode"></i> ${sparePart.part_number}</p>` : ''}
            </div>
            <div class="stock-status">
                <span class="stock-badge ${stockClass}">${sparePart.quantity_in_stock}</span>
                <span class="stock-label">قطعة</span>
            </div>
        </div>
        
        <div class="spare-part-details">
            <div class="detail-row">
                <span class="detail-label">سعر الشراء:</span>
                <span class="detail-value">${formatCurrency(sparePart.cost_price)}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">سعر البيع:</span>
                <span class="detail-value">${formatCurrency(sparePart.selling_price)}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">الحد الأدنى:</span>
                <span class="detail-value">${sparePart.minimum_stock_level} قطعة</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">الربح:</span>
                <span class="detail-value profit">${formatCurrency(sparePart.selling_price - sparePart.cost_price)}</span>
            </div>
        </div>
        
        <div class="spare-part-footer">
            <div class="stock-status-text">
                <span class="${stockClass}">${stockStatus}</span>
            </div>
            <div class="spare-part-actions">
                <button class="btn-icon" onclick="adjustStock(${sparePart.id})" title="تعديل المخزون">
                    <i class="fas fa-boxes"></i>
                </button>
                <button class="btn-icon" onclick="editSparePart(${sparePart.id})" title="تعديل">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn-icon btn-danger" onclick="deleteSparePart(${sparePart.id})" title="حذف">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
    `;
    
    return div;
}

// الحصول على أيقونة الفئة
function getCategoryIcon(category) {
    const icons = {
        'شاشات': 'fa-tv',
        'بطاريات': 'fa-battery-half',
        'كابلات': 'fa-plug',
        'شواحن': 'fa-charging-station',
        'سماعات': 'fa-headphones',
        'كاميرات': 'fa-camera',
        'أزرار': 'fa-circle',
        'مكبرات صوت': 'fa-volume-up',
        'أخرى': 'fa-cog'
    };
    return icons[category] || 'fa-cog';
}

// الحصول على حالة المخزون
function getStockStatus(sparePart) {
    if (sparePart.quantity_in_stock === 0) {
        return 'نفد المخزون';
    } else if (sparePart.quantity_in_stock <= sparePart.minimum_stock_level) {
        return 'مخزون منخفض';
    } else {
        return 'متوفر';
    }
}

// الحصول على فئة المخزون
function getStockClass(sparePart) {
    if (sparePart.quantity_in_stock === 0) {
        return 'stock-out';
    } else if (sparePart.quantity_in_stock <= sparePart.minimum_stock_level) {
        return 'stock-low';
    } else {
        return 'stock-good';
    }
}

// إظهار نافذة إضافة قطعة غيار
function showAddSparePartModal() {
    isEditMode = false;
    currentSparePart = null;
    
    const modal = document.getElementById('sparePartModal');
    const form = document.getElementById('sparePartForm');
    const title = document.getElementById('sparePartModalTitle');
    
    title.textContent = 'إضافة قطعة غيار جديدة';
    form.reset();
    
    modal.classList.add('show');
}

// تعديل قطعة الغيار
async function editSparePart(sparePartId) {
    try {
        const sparePart = await ipcRenderer.invoke('database-get',
            'SELECT * FROM spare_parts WHERE id = ?',
            [sparePartId]
        );
        
        if (!sparePart) {
            showNotification('قطعة الغيار غير موجودة', 'error');
            return;
        }
        
        isEditMode = true;
        currentSparePart = sparePart;
        
        const modal = document.getElementById('sparePartModal');
        const form = document.getElementById('sparePartForm');
        const title = document.getElementById('sparePartModalTitle');
        
        title.textContent = 'تعديل قطعة الغيار';
        
        // ملء النموذج بالبيانات الحالية
        document.getElementById('sparePartName').value = sparePart.name;
        document.getElementById('sparePartCategory').value = sparePart.category;
        document.getElementById('sparePartBrand').value = sparePart.brand || '';
        document.getElementById('sparePartModel').value = sparePart.model || '';
        document.getElementById('partNumber').value = sparePart.part_number || '';
        document.getElementById('costPrice').value = sparePart.cost_price;
        document.getElementById('sellingPrice').value = sparePart.selling_price;
        document.getElementById('quantityInStock').value = sparePart.quantity_in_stock;
        document.getElementById('minimumStockLevel').value = sparePart.minimum_stock_level;
        document.getElementById('supplier').value = sparePart.supplier || '';
        document.getElementById('sparePartNotes').value = sparePart.notes || '';
        
        modal.classList.add('show');
        
    } catch (error) {
        console.error('خطأ في تحميل بيانات قطعة الغيار للتعديل:', error);
        showNotification('حدث خطأ في تحميل بيانات قطعة الغيار', 'error');
    }
}

// حذف قطعة الغيار
async function deleteSparePart(sparePartId) {
    if (!confirm('هل أنت متأكد من حذف قطعة الغيار هذه؟')) {
        return;
    }
    
    try {
        await ipcRenderer.invoke('database-run',
            'DELETE FROM spare_parts WHERE id = ?',
            [sparePartId]
        );
        
        showNotification('تم حذف قطعة الغيار بنجاح', 'success');
        loadSpareParts();
        
    } catch (error) {
        console.error('خطأ في حذف قطعة الغيار:', error);
        showNotification('حدث خطأ في حذف قطعة الغيار', 'error');
    }
}

// تعديل المخزون
async function adjustStock(sparePartId) {
    const sparePart = spareParts.find(sp => sp.id === sparePartId);
    if (!sparePart) return;
    
    const newQuantity = prompt(`الكمية الحالية: ${sparePart.quantity_in_stock}\nأدخل الكمية الجديدة:`, sparePart.quantity_in_stock);
    
    if (newQuantity === null || newQuantity === '') return;
    
    const quantity = parseInt(newQuantity);
    if (isNaN(quantity) || quantity < 0) {
        showNotification('يرجى إدخال كمية صحيحة', 'error');
        return;
    }
    
    try {
        await ipcRenderer.invoke('database-run',
            'UPDATE spare_parts SET quantity_in_stock = ?, updated_at = ? WHERE id = ?',
            [quantity, new Date().toISOString(), sparePartId]
        );
        
        showNotification('تم تحديث المخزون بنجاح', 'success');
        loadSpareParts();
        
    } catch (error) {
        console.error('خطأ في تحديث المخزون:', error);
        showNotification('حدث خطأ في تحديث المخزون', 'error');
    }
}

// معالج إرسال نموذج قطعة الغيار
async function handleSparePartSubmit(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const sparePartData = {
        name: formData.get('name').trim(),
        category: formData.get('category'),
        brand: formData.get('brand').trim(),
        model: formData.get('model').trim(),
        part_number: formData.get('part_number').trim(),
        cost_price: parseFloat(formData.get('cost_price')) || 0,
        selling_price: parseFloat(formData.get('selling_price')) || 0,
        quantity_in_stock: parseInt(formData.get('quantity_in_stock')) || 0,
        minimum_stock_level: parseInt(formData.get('minimum_stock_level')) || 5,
        supplier: formData.get('supplier').trim(),
        notes: formData.get('notes').trim()
    };
    
    // التحقق من صحة البيانات
    if (!validateSparePartData(sparePartData)) {
        return;
    }
    
    try {
        if (isEditMode && currentSparePart) {
            // تحديث قطعة الغيار
            await ipcRenderer.invoke('database-run', `
                UPDATE spare_parts SET 
                    name = ?, category = ?, brand = ?, model = ?, part_number = ?,
                    cost_price = ?, selling_price = ?, quantity_in_stock = ?, 
                    minimum_stock_level = ?, supplier = ?, notes = ?, updated_at = ?
                WHERE id = ?
            `, [
                sparePartData.name, sparePartData.category, sparePartData.brand,
                sparePartData.model, sparePartData.part_number, sparePartData.cost_price,
                sparePartData.selling_price, sparePartData.quantity_in_stock,
                sparePartData.minimum_stock_level, sparePartData.supplier,
                sparePartData.notes, new Date().toISOString(), currentSparePart.id
            ]);
            
            showNotification('تم تحديث قطعة الغيار بنجاح', 'success');
        } else {
            // إضافة قطعة غيار جديدة
            await ipcRenderer.invoke('database-run', `
                INSERT INTO spare_parts (
                    name, category, brand, model, part_number, cost_price,
                    selling_price, quantity_in_stock, minimum_stock_level,
                    supplier, notes
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `, [
                sparePartData.name, sparePartData.category, sparePartData.brand,
                sparePartData.model, sparePartData.part_number, sparePartData.cost_price,
                sparePartData.selling_price, sparePartData.quantity_in_stock,
                sparePartData.minimum_stock_level, sparePartData.supplier, sparePartData.notes
            ]);
            
            showNotification('تم إضافة قطعة الغيار بنجاح', 'success');
        }
        
        hideSparePartModal();
        loadSpareParts();
        
    } catch (error) {
        console.error('خطأ في حفظ بيانات قطعة الغيار:', error);
        showNotification('حدث خطأ في حفظ بيانات قطعة الغيار', 'error');
    }
}

// التحقق من صحة بيانات قطعة الغيار
function validateSparePartData(data) {
    if (!data.name) {
        showNotification('يرجى إدخال اسم قطعة الغيار', 'error');
        return false;
    }
    
    if (!data.category) {
        showNotification('يرجى اختيار فئة قطعة الغيار', 'error');
        return false;
    }
    
    if (data.cost_price < 0) {
        showNotification('سعر الشراء لا يمكن أن يكون سالباً', 'error');
        return false;
    }
    
    if (data.selling_price < 0) {
        showNotification('سعر البيع لا يمكن أن يكون سالباً', 'error');
        return false;
    }
    
    if (data.quantity_in_stock < 0) {
        showNotification('الكمية لا يمكن أن تكون سالبة', 'error');
        return false;
    }
    
    return true;
}

// البحث في قطع الغيار
function handleSparePartSearch(e) {
    const searchTerm = e.target.value.toLowerCase().trim();
    
    if (!searchTerm) {
        displaySpareParts(spareParts);
        return;
    }
    
    const filteredSpareParts = spareParts.filter(sparePart => 
        sparePart.name.toLowerCase().includes(searchTerm) ||
        sparePart.category.toLowerCase().includes(searchTerm) ||
        (sparePart.brand && sparePart.brand.toLowerCase().includes(searchTerm)) ||
        (sparePart.part_number && sparePart.part_number.toLowerCase().includes(searchTerm))
    );
    
    displaySpareParts(filteredSpareParts);
}

// فلترة حسب الفئة
function handleCategoryFilter(e) {
    const category = e.target.value;
    
    if (!category) {
        displaySpareParts(spareParts);
        return;
    }
    
    const filteredSpareParts = spareParts.filter(sparePart => sparePart.category === category);
    displaySpareParts(filteredSpareParts);
}

// فلترة حسب المخزون
function handleStockFilter(e) {
    const filter = e.target.value;
    
    let filteredSpareParts = spareParts;
    
    if (filter === 'low') {
        filteredSpareParts = spareParts.filter(sp => sp.quantity_in_stock <= sp.minimum_stock_level);
    } else if (filter === 'out') {
        filteredSpareParts = spareParts.filter(sp => sp.quantity_in_stock === 0);
    } else if (filter === 'good') {
        filteredSpareParts = spareParts.filter(sp => sp.quantity_in_stock > sp.minimum_stock_level);
    }
    
    displaySpareParts(filteredSpareParts);
}

// تحديث إحصائيات قطع الغيار
function updateSparePartsStats() {
    const totalParts = spareParts.length;
    const lowStockParts = spareParts.filter(sp => sp.quantity_in_stock <= sp.minimum_stock_level).length;
    const outOfStockParts = spareParts.filter(sp => sp.quantity_in_stock === 0).length;
    const totalValue = spareParts.reduce((sum, sp) => sum + (sp.quantity_in_stock * sp.cost_price), 0);
    
    // تحديث العناصر في الواجهة
    const totalElement = document.getElementById('totalPartsCount');
    const lowStockElement = document.getElementById('lowStockCount');
    const outOfStockElement = document.getElementById('outOfStockCount');
    const totalValueElement = document.getElementById('totalStockValue');
    
    if (totalElement) totalElement.textContent = totalParts;
    if (lowStockElement) lowStockElement.textContent = lowStockParts;
    if (outOfStockElement) outOfStockElement.textContent = outOfStockParts;
    if (totalValueElement) totalValueElement.textContent = formatCurrency(totalValue);
}

// إخفاء نافذة قطعة الغيار
function hideSparePartModal() {
    const modal = document.getElementById('sparePartModal');
    modal.classList.remove('show');
}

// دوال مساعدة
function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-SA', {
        style: 'decimal',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(amount || 0) + ' ريال';
}

function showNotification(message, type = 'info') {
    if (type === 'error') {
        alert('خطأ: ' + message);
    } else if (type === 'success') {
        alert('نجح: ' + message);
    } else {
        alert(message);
    }
}

// تصدير الدوال
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        initializeSpareParts,
        showAddSparePartModal,
        hideSparePartModal
    };
}
